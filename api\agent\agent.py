from datetime import datetime
from agents import ModelSettings
from api.agent.agent_brain import modo_resposta_ia
from api.agent.util.functions.texto_compactar import texto_compactar
from api.agent.util.functions.tokens_contador import tokens_contador
from api.agent.util.functions.tokens_contador import tokens_contador
from api.agent.converter.functions.text_to_speech import text_to_speech

try:
    from agents.tool import FunctionTool
    HAS_NEW_API = True
except ImportError:
    from agents import FunctionTool
    HAS_NEW_API = False
import pytz
import re
import importlib
import inspect
import json
from urllib3 import response
from .agent_logger import AgentLogger
from .agent_neo4j import AgentNeo4j
from .agent_openai import OpenAi
from fastapi import APIRouter, Request
from .agent_llm import LLM
from fastapi.responses import StreamingResponse, JSONResponse
from typing import Optional, Any, Dict, AsyncGenerator
from ..cache import cache
from ..functions.util import generate_unique_id
from .agent_message import Message
from threading import Lock
import json
from datetime import datetime

# Inicialização de componentes globais
logger = AgentLogger()
router = APIRouter()
neo4j = AgentNeo4j()
llm = LLM()
oai = OpenAi()
tokens_maximo = 0

# ✅ ADICIONAR VARIÁVEIS GLOBAIS DO CACHE
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()
canal = ""

fuso_brasilia = pytz.timezone('America/Sao_Paulo')




#
async def cria_ativa_conversa(usuario_idx: str, agente_idx: str) -> tuple:
    """
    Processos desta funcao
    1 - Procurar no neo4j por Conversa com Status com propriedades  nome = "Em Andamento" e ativo = true
    1.1 - Se encontrou:
    1.1.1 - Carrregar todas as Mensagem (Conversa-TEM_MENSAGEM) da Conversa  em ordem crescente da propriedade data_hora 
    1.1.2 - Criar um array local chamado history
    1.1.3 - Adicionar um objeto no array corresponde a cada mensagem ,obedenco ao seguinte criterio:
          Se a Mensagem-ENVIADO_POR-> for Agente, a chave "role" tera o valor "assistant" con contrario sera "user"
          Adicionar o texto da mensagem para a cahave "content"
    1.1.4  Retornar o idx  o idx da Conversa e o histório
   1.2 Se não encontrou:          
   1.2.1 Criar uma Nova Conversa com um novo idx (generate_unique_id()) e com o seguinte relacionamento
       Pessoa (idx=usuario_idx)-TEM_CONVERSA->Agente(idx=agente_idx)
       Conversa->TEM_STATUS {ativo=true, entrada: DATETIME}->Status(nome="Em Andamento"  ) 
   1.2.2  
       Retornar o idx da conversa e historico com valor []
   Procura por uma conversa em andamento com o agente atual
   usar o neo4j (agent_neo4j ja instanciado no entrada do arquivo) para exexutar as cyphers
    """
    print("===== cria_ativa_conversa =====")
    global tokens_maximo
    #print("usuario_idx", usuario_idx)
    #print("agente_idx", agente_idx)
    # 1 - Procurar conversa ativa
    import asyncio
    # 1 - Procurar conversa ativa
    cypher_find = (
        "MATCH (p:Pessoa {idx: $usuario_idx})-[:TEM_CONVERSA]->(c:Conversa)-[:TEM_STATUS]->(s:Status) "
        "WHERE s.nome = 'Em Andamento' AND s.ativo = true "
        "AND (c)<-[:TEM_CONVERSA]-(:Agente {idx: $agente_idx}) "
        "RETURN c.idx AS conversa_idx"
         )
    #print("cypher_find:", cypher_find)
    
    loop = asyncio.get_event_loop()
    result = await neo4j.execute_read_query(cypher_find, {'usuario_idx': usuario_idx, 'agente_idx': agente_idx})
    #print("Resultado da busca Cypher:", result)
    record = result[0] if result else None
    if record and record.get('conversa_idx'):
        conversa_idx = record['conversa_idx']
        cypher_msg = (
            "MATCH (c:Conversa {idx: $conversa_idx})-[:TEM_MENSAGEM]->(m:Mensagem) "
            "OPTIONAL MATCH (m)-[:ENVIADO_POR]->(ag:Agente) "
            "WITH m, ag "
            "ORDER BY m.data_hora ASC "
            "RETURN m.role AS role, m.texto_compactado AS texto, m.data_hora AS data_hora, m.tks_comp AS tks_comp, ag.idx AS agente_id"
        )
        msgs = await neo4j.execute_read_query(cypher_msg, {'conversa_idx': conversa_idx})

        if len(msgs) > 0:
            if len(msgs) > 50:
                msgs = msgs[-50:]
            msgs = list(reversed(msgs))
            history = []
            total_tokens = 0
            total_msgs = len(msgs)
            #print("total_msgs:", total_msgs)
            #print("tokens_maximo:", tokens_maximo)
            for msg in msgs:
                role = 'assistant' if msg.get('agente_id') else 'user'
                msg_tokens = msg['tks_comp']
                if total_tokens + msg_tokens > tokens_maximo:
                    #print("Limite de tokens atingido, parando de adicionar mensagens")
                    break
                total_tokens += msg_tokens
                #print("total_tokens:", total_tokens)
                history.append({
                    'role': role,
                    'content': msg['texto']
                })
                texto = msg['texto']
                #print("role:", msg['role'])
                #print("data_hora", msg['data_hora'])
                #print("texto", texto[:20] + "..." if len(texto) > 20 else texto)
            history = list(reversed(history))
            #print("Total de mensagens no histórico:", len(history))
            return conversa_idx, history
        else:
            # Nenhuma mensagem encontrada, retorna conversa_idx e lista vazia
            return conversa_idx, []
    else:
        conversa_idx = generate_unique_id()
        cypher_create = (
            "MERGE (p:Pessoa {idx: $usuario_idx}) "
            "MERGE (a:Agente {idx: $agente_idx}) "
            "CREATE (p)-[:TEM_CONVERSA]->(c:Conversa {idx: $conversa_idx}), "
            "      (a)-[:TEM_CONVERSA]->(c), "
            "      (c)-[:TEM_STATUS {ativo: true, entrada: datetime()}]->(s:Status {nome: 'Em Andamento', ativo: true}), "
            "      (c)-[:INICIADA_POR]->(p) "
            "RETURN c.idx AS conversa_idx"
        )
        result_create = await neo4j.execute_write_query(cypher_create, {'usuario_idx': usuario_idx, 'agente_idx': agente_idx, 'conversa_idx': conversa_idx})
        verify_query = (
            "MATCH (p:Pessoa {idx: $usuario_idx})-[:TEM_CONVERSA]->(c:Conversa {idx: $conversa_idx})-[:TEM_STATUS]->(s:Status) "
            "WHERE s.nome = 'Em Andamento' AND s.ativo = true "
            "RETURN c.idx AS conversa_idx, s.nome AS status, s.ativo AS ativo"
        )
        verify_result = await neo4j.execute_read_query(verify_query, {'usuario_idx': usuario_idx, 'conversa_idx': conversa_idx})
        return conversa_idx, []
    # Garantir retorno seguro em qualquer fluxo
    return None, []



def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history

def limpar_tags_agente(texto: str) -> str:
    """Remove tags incompletas do agente"""
    if not texto:
        return ""
    
    # Remove tags HTML incompletas no final
    texto = re.sub(r'<[^>]*$', '', texto)
    
    # Remove tags de abertura sem fechamento
    texto = re.sub(r'<([^/>]+)>(?![^<]*</\1>)', '', texto)
    
    return texto.strip()

async def registra_acesso_agente(self, usuario_idx: str, agente_idx: str, canal_idx: str):
        """
        Registra o acesso do usuário ao agente
        """
        print("@@@@@ registra_acesso_agente", usuario_idx, agente_idx, canal_idx)
        query = """
        MATCH (p:Pessoa {idx: $usuario_idx})
        MATCH (ag:Agente {idx: $agente_idx})
        MATCH (can:Canal {idx: $canal_idx})
        CREATE (p)-[:TEVE_ACESSO {data_hora: datetime()}]->(ac:Acesso)
        CREATE (ac)-[:AO_AGENTE]->(ag)
        CREATE (ac)-[:USANDO_O_CANAL]->(can)
        """
        params = {"usuario_idx": usuario_idx, "agente_idx": agente_idx, "canal_idx": canal_idx}
        result = await neo4j.execute_write_query(query, params)
        print("result", result)
        return result


def extrair_texto_puro(html_text: str) -> str:
    """Extrai texto puro removendo todas as tags HTML"""
    if not html_text:
        return ""
    
    # Remove todas as tags HTML
    texto_limpo = re.sub(r'<[^>]+>', '', html_text)
    
    # Remove espaços extras e quebras de linha desnecessárias
    texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
    
    return texto_limpo.strip()

def limpar_markdown_Whatsapp(texto: str) -> str:
    """Remove formatação Markdown para Whatsapp, mantendo apenas URLs dos links"""
    if not texto:
        return ""
    
    import re
    
    # Remove formatação Markdown mantendo apenas URLs dos links
    # Links [texto](url) -> url
    texto = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'\2', texto)
    
    # Remove negrito **texto** ou __texto__
    #texto = re.sub(r'\*\*([^*]+)\*\*', r'\1', texto)
    #texto = re.sub(r'__([^_]+)__', r'\1', texto)
    
    # Remove itálico *texto* ou _texto_
    #texto = re.sub(r'\*([^*]+)\*', r'\1', texto)
    #texto = re.sub(r'_([^_]+)_', r'\1', texto)
    
    # Remove código `texto`
    #texto = re.sub(r'`([^`]+)`', r'\1', texto)
    
    # Remove cabeçalhos # ## ###
    #texto = re.sub(r'^#{1,6}\s*', '', texto, flags=re.MULTILINE)
    
    # Remove listas - ou *
    #texto = re.sub(r'^[\s]*[-*]\s*', '', texto, flags=re.MULTILINE)
    
    # Remove espaços extras
    #texto = re.sub(r'\s+', ' ', texto)
    
    return texto.strip()

class Agent:
    """
    Classe base Agent - Estrutura genérica para criação de agentes
    Baseada no padrão do AgentOficinatech
    """
    def __init__(
        self,
        modelo = None,
        agente_idx: Optional[str] = None,
        agente_nome: Optional[str] = None,
        agente_instrucoes: Optional[str] = None,
        agente_identidade: Optional[str] = None,
        agente_personalidade: Optional[str] = None,
        agente_estilo_conversa: Optional[str] = None,
        conversa_idx: str = None,
        em_resposta_idx: str = None,
        usuario_nome: Optional[str] = None,
        usuario_idx: Optional[str] = None,
        usuario_whatsapp: Optional[str] = None,
        imagem: Optional[str] = None,
        negocio_idx: Optional[str] = None,
        negocio_nome: Optional[str] = None,
        conhecimento_idx: Optional[str] = None,
        plataforma_url: Optional[str] = None,
        ferramentas: Optional[list] = None,
        canal_idx: Optional[str] = None,
    ):
        """
        Inicializa o agente com parâmetros básicos
        
        Args:
            modelo: Modelo de IA a ser usado
            agente_idx: ID do agente
            agente_nome: Nome do agente
            agente_instrucoes: Instruções do agente
            agente_identidade: Identidade do agente
            agente_personalidade: Personalidade do agente   
            agente_estilo_conversa: Estilo de conversa do agente
            conversa_idx: idx da conversa atual
            em_resposta_idx: idx da mensagem atual a ser respondida
            usuario_nome: Nome do usuário
            usuario_idx: ID do usuário
            usuario_whatsapp: Telefone do usuário
            imagem: Imagem do usuário   
            conhecimento_idx: ID do conhecimento
            negocio_idx: ID do negócio
            negocio_nome: Nome do negócio
            plataforma_url: URL da plataforma
            ferramentas: Ferramentas do agente
            canal_idx: ID do canal
        """
        #logger.info(f"===== Agent({agente_nome}) =====")
        
        # Data e hora atual no fuso de Brasília
        data_hora = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
        
        # Atributos básicos do agente
        self.modelo = modelo  # ← ADICIONAR ESTA LINHA
        self.agente_idx = agente_idx
        self.agente_nome = agente_nome
        self.agente_instrucoes = agente_instrucoes
        self.agente_personalidade = agente_personalidade
        self.agente_identidade = agente_identidade
        self.agente_estilo_conversa = agente_estilo_conversa
        self.conversa_idx = conversa_idx
        self.em_resposta_idx = em_resposta_idx 
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.usuario_whatsapp = usuario_whatsapp
        self.imagem = imagem
        self.negocio_idx = negocio_idx
        self.conhecimento_idx = conhecimento_idx
        self.plataforma_url = plataforma_url
        self.data_hora = data_hora
        self.ferramentas = ferramentas
        self.canal_idx = canal_idx

        self.runner = {}
        # Componentes do agente
        self.router = APIRouter()
        self.oai = OpenAi()
        self.neo4j = AgentNeo4j()

        self.seta_instrucoes()

        
    def seta_instrucoes(self):
        negocio_idx = "negocio_idx = " + self.negocio_idx if self.negocio_idx else ""
        agente_idx = "agente_idx = " + self.agente_idx if self.agente_idx else ""
        conhecimento_idx = "conhecimento_idx = " + self.conhecimento_idx if self.conhecimento_idx else ""

        usuario_whatsapp = "O meu telefone é " + self.usuario_whatsapp if self.usuario_whatsapp else ""
        usuario_idx = "O meu usuario_idx é " + self.usuario_idx if self.usuario_idx else ""
        data_hora = "A data e hora atual é " + self.data_hora if self.data_hora else ""
        conversa_idx = "O idx da conversa atual é " + self.conversa_idx if self.conversa_idx else ""
        em_resposta_idx = "o idx da mensagem a ser respondida (em_resposta_idx) é " + self.em_resposta_idx if self.em_resposta_idx else ""
        plataforma_url = "A url da plataforma é " + self.plataforma_url if self.plataforma_url else "",
        canal_idx = "canal_idx = " + self.canal_idx if self.canal_idx else ""

        self.instrucoes = f"""
        Seu nome é {self.agente_nome}
        {self.agente_identidade}
        {self.agente_personalidade}
        {self.agente_instrucoes}
        No momento você está atendendo a mim. Meu nome é  {self.usuario_nome}.
        Outras informações úteis que  deverá utilizar sempre que uma das suas funções solicitar:
        {negocio_idx}
        {agente_idx}
        {usuario_idx}
        {usuario_whatsapp}
        {plataforma_url}
        {conhecimento_idx}
        {data_hora}
        {conversa_idx}
        {em_resposta_idx}
        {canal_idx} 
        
        SOBRE AS FUNÇÕES:
        🚨🚨🚨 A função entrada_estoque() é uma função de entrada de produto, logo, não é necessário verificar se os produtos tem estoque, nem solicitar ou informar isto ao usuário.
        
        🚨🚨🚨 FLUXO CORRETO para entrada_estoque():
        1. Usuário informa nome ou código do produto
        2. AGENTE busca o produto no catálogo usando consultar_catalogo_produtos() 
        3. AGENTE obtém automaticamente: nome, código, preço do catálogo
        4. AGENTE pergunta apenas a QUANTIDADE ao usuário
        5. AGENTE adiciona o produto com preço do catálogo + quantidade informada
        
        Esta função deverá ser chamada sempre que o usuário pedir coisas como:
        -fazer nova entrada
        -Nova entrada
        -Entrar com estoque


        FIQUE ATENTO PARA NÃO CONFUNDIR ESTAS 2 FUNÇÕES:
        entrada_estoque() e  produto_catalogo_adicionar() 
        Veja as diferenças:
        entrada_esotque() é utilizda para registrar as entrada de produtos do negocio no estoque de revenda da consultora.  termos que a acionam:
        -fazer nova entrada
        -nova entrada
        -entrada d eesotque
        
        produto_catoloog_adicionar() é utilizada para adicionar novos produtos no catalogo do negocio que fornece os produtos. ntermos que a adiconam:
        -novo produto
        -adicionar produto

        """


    async def get_agent_openai(self) -> Any:
        # Configurar ModelSettings para baixa criatividade
        #print("==== get_agent_openai()=====")
        model_settings = ModelSettings(
            temperature=0.1,          # Muito baixo para respostas determinísticas
            top_p=0.3,               # Limita as opções de palavras
            frequency_penalty=0.1,    # Evita repetições
            presence_penalty=0.3,     # Encoraja consistência
            max_tokens=4000          # Aumenta o limite de tokens para respostas mais longas
        )
        
        
        #print("name", self.agente_nome)
        #print("model", self.modelo)
        #print("model_settings", model_settings)
        #print("instructions", self.instrucoes)
        agent_openai = {
            "name": self.agente_nome,
            "instructions": self.instrucoes,
            "model": self.modelo,
            "model_settings": model_settings,  # Adicione esta linha
            "tools": self.ferramentas or [],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
        }

        agente_openai_objeto = await self.oai.agent_create(**agent_openai)
        return agente_openai_objeto

def _to_str_dict(data: Any) -> Dict[str, Any]:
    """Converte um mapping possivelmente com chaves/valores bytes para dict[str, Any]."""
    try:
        if isinstance(data, dict):
            return {str(k): (v.decode('utf-8', 'ignore') if isinstance(v, (bytes, bytearray)) else v) for k, v in data.items()}
        if hasattr(data, 'items'):
            obj = dict(data)
            return {str(k): (v.decode('utf-8', 'ignore') if isinstance(v, (bytes, bytearray)) else v) for k, v in obj.items()}
    except Exception:
        pass
    return {}

async def carrega_canal(canal_idx: str) -> str:
    """
    Carrega o canal do banco de dados Neo4j.
    
    Args:
        canal_idx: ID do canal a ser carregado
        
    Returns:
        Canal.nome: Nome do canal carregado do banco de dados
        
    Raises:
        ValueError: Se o canal não for encontrado
    """
    
    query = """
    MATCH (c:Canal {idx: $canal_idx}) 
    RETURN c.nome as nome
    """
    
    result = await neo4j.execute_read_query(query, {"canal_idx": canal_idx})
    
    if result and len(result) > 0:
        return result[0].get('nome')
    
    # Return default channel if not found
    return "app_web"


async def nova_conversa(conversa_idx: str = None, usuario_idx: str = None, agente_idx: str = None) -> str:
    """
    Encerra a conversa no banco de dados Neo4j.
    
    Args:
        conversa_idx: ID da conversa à ser encerrada (opcional)
        usuario_idx: ID do usuário para localizar conversa ativa (opcional se conversa_idx fornecido)

        agente_idx: ID do agente para localizar conversa ativa (opcional se conversa_idx fornecido)
    
    Returns:
        str: ID da conversa que foi encerrada
    """
    print("@1@@1@@ conversa_encerrada() =======================")
    print("conversa_idx", conversa_idx)
    print("usuario_idx", usuario_idx)
    print("agente_idx", agente_idx)
    
    # 1 - Localizar a Conversa
    if conversa_idx:
        # 1.1 - Se o idx for informado, localizar diretamente
        query_verificar = """
        MATCH (c:Conversa {idx: $conversa_idx})
        RETURN c.idx AS conversa_idx
        """
        result = await neo4j.execute_read_query(query_verificar, {"conversa_idx": conversa_idx})
        if not result:
            raise ValueError(f"Conversa com idx {conversa_idx} não encontrada")
        conversa_id = conversa_idx
    else:
        # 1.2 - Se não informado, localizar conversa com status Em Andamento ativo
        if not usuario_idx or not agente_idx:
            raise ValueError("usuario_idx e agente_idx são obrigatórios quando conversa_idx não for fornecido")
            
        query_localizar = """
        MATCH (p:Pessoa {idx: $usuario_idx})-[:TEM_CONVERSA]->(c:Conversa)-[:TEM_STATUS]->(s:Status)
        WHERE s.nome = 'Em Andamento' AND s.ativo = true
        AND (c)<-[:TEM_CONVERSA]-(:Agente {idx: $agente_idx})
        RETURN c.idx AS conversa_idx
        """
        result = await neo4j.execute_read_query(query_localizar, {
            'usuario_idx': usuario_idx,
            'agente_idx': agente_idx
        })
        if not result:
            raise ValueError("Nenhuma conversa ativa encontrada para este usuário e agente")
        conversa_id = result[0]['conversa_idx']
    
    # 2 - Mudar a propriedade Ativo para False do Status Em Andamento
    query_atualizar = """
    MATCH (c:Conversa {idx: $conversa_idx})-[:TEM_STATUS]->(s:Status)
    WHERE s.nome = 'Em Andamento'
    SET s.ativo = false
    """
    await neo4j.execute_write_query(query_atualizar, {"conversa_idx": conversa_id})
    
    # 3 - Adicionar o Status Concluido
    query_adicionar = """
    MATCH (c:Conversa {idx: $conversa_idx})
    CREATE (c)-[:TEM_STATUS {ativo: true, entrada: datetime()}]->(s:Status {nome: 'Concluido'})
    """
    await neo4j.execute_write_query(query_adicionar, {"conversa_idx": conversa_id})
    
    print(f"Conversa {conversa_id} encerrada com sucesso")
    return conversa_id


async def salva_mensagem(mensagem: str, conversa_idx: str, usuario_idx: str = None, agente_idx: str = None, em_resposta_idx: str = None, remetente: str = None,destinatario: str = None, canal_idx: str = None) -> str:
    """
    Salva a mensagem do usuário ou do agente no banco de dados Neo4j.
    
    Args:
        mensagem: Texto da mensagem a ser salva
        conversa_idx: ID da conversa à qual a mensagem pertence
        usuario_idx: ID do usuário remetente (opcional)
        agente_idx: ID do agente remetente (opcional)
        em_resposta_idx: IDX da mensagem que esta sendo respondida
        remetente: Especifica explicitamente quem enviou ('usuario' ou 'agente')
        destinatario: Especifica explicitamente quem é o destinatário ('usuario' ou 'agente')
        canal_idx: ID do canal
        
    Returns:
        str: ID da mensagem salva
        
    Raises:
        ValueError: Se as validações iniciais falharem
    """
    #print("@1@@1@@ salva_mensagem() =======================")
    #print("conversa_idx", conversa_idx)
    #print("usuario_idx", usuario_idx)
    #print("agente_idx", agente_idx)
    #print("em_resposta_idx", em_resposta_idx)
    #print("mensagem", mensagem)
    #print("canal_idx", canal_idx)


    # Validações iniciais
    if not mensagem or not mensagem.strip():
        raise ValueError("A mensagem não pode estar vazia")
        
    if not conversa_idx:
        raise ValueError("O ID da conversa é obrigatório")
        
    if not usuario_idx and not agente_idx:
        raise ValueError("É necessário informar o ID do usuário ou do agente")
    
    # Determina o tipo de remetente
    if remetente:
        enviador = remetente
    else:
        enviador = "usuario" if usuario_idx else "agente"
    
    # Gera um ID único para a mensagem
    mensagem_idx = generate_unique_id()
    #tks_normal, tks_comp, mensagem_compactada = await texto_compactar(mensagem)
    #tks_normal, tks_comp, mensagem_compactada = await texto_compactar(mensagem)
    tks_normal = tokens_contador(mensagem)
    tks_comp = tks_normal
    mensagem_compactada = mensagem

    # Obtém a data e hora atuais
    data_hora = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    
    # Determina qual query usar baseado no remetente
    if enviador == "usuario":
        remetente_query = "MERGE (p:Pessoa {idx: $usuario_idx}) CREATE (m)-[:ENVIADO_POR]->(p)"
    else:
        remetente_query = "MERGE (a:Agente {idx: $agente_idx}) CREATE (m)-[:ENVIADO_POR]->(a)"
    
    # Determina o relacionamento ENVIADO_PARA baseado no destinatário
    if destinatario:
        if destinatario == "agente":
            destinatario_query = "MERGE (a:Agente {idx: $agente_idx}) CREATE (m)-[:ENVIADO_PARA]->(a)"
        else:  # usuario
            destinatario_query = "MERGE (p:Pessoa {idx: $usuario_idx}) CREATE (m)-[:ENVIADO_PARA]->(p)"
    else:
        # Se não especificado, usa a lógica inversa do remetente
        if enviador == "usuario":
            destinatario_query = "MERGE (a:Agente {idx: $agente_idx}) CREATE (m)-[:ENVIADO_PARA]->(a)"
        else:
            destinatario_query = "MERGE (p:Pessoa {idx: $usuario_idx}) CREATE (m)-[:ENVIADO_PARA]->(p)"
    
    # Constrói partes condicionais da query
    query_parts = []

    # Parte base da query
    base_query = f"""
    // Cria o nó Mensagem
    CREATE (m:Mensagem {{
        idx: $mensagem_idx,
        texto: $mensagem,
        texto_compactado: $mensagem_compactada,
        tks_normal: $tks_normal,
        tks_comp: $tks_comp,
        data_hora: datetime($data_hora),
        tipo: $enviador
    }})

    // Relaciona com a Conversa
    WITH m
    MATCH (c:Conversa {{idx: $conversa_idx}})
    CREATE (c)-[:TEM_MENSAGEM]->(m)
    """
    query_parts.append(base_query)

    # Relacionamento EM_RESPOSTA se informado
    if em_resposta_idx and em_resposta_idx.strip():
        query_parts.append(f"""
    // Cria o relacionamento EM_RESPOSTA se informado
    WITH m
    MATCH (msg_original:Mensagem {{idx: $em_resposta_idx}})
    CREATE (m)-[:EM_RESPOSTA]->(msg_original)
    """)

    # Relacionamento USANDO_O_CANAL se canal_idx for fornecido
    if canal_idx and canal_idx.strip():
        query_parts.append("""
    // Cria o relacionamento com o Canal
    WITH m
    MATCH (can:Canal {idx: $canal_idx})
    CREATE (m)-[:USANDO_O_CANAL]->(can)
    """)

    # Relacionamento com o remetente
    query_parts.append(f"""
    // Cria o relacionamento com o remetente
    WITH m
    {remetente_query}
    """)

    # Relacionamento com o destinatário
    query_parts.append(f"""
    // Cria o relacionamento com o destinatário
    WITH m
    {destinatario_query}

    RETURN m.idx as mensagem_idx
    """)

    # Junta todas as partes
    query = "".join(query_parts)
    
    try:
        # Prepara os parâmetros
        params = {
            "mensagem_idx": mensagem_idx,
            "mensagem": mensagem,
            "mensagem_compactada": mensagem_compactada,
            "tks_normal": tks_normal,
            "tks_comp": tks_comp,
            "data_hora": data_hora,
            "conversa_idx": conversa_idx,
            "usuario_idx": usuario_idx,
            "agente_idx": agente_idx,
            "enviador": enviador,
            "em_resposta_idx": em_resposta_idx
        }

        # Adiciona canal_idx aos parâmetros se fornecido
        if canal_idx and canal_idx.strip():
            params["canal_idx"] = canal_idx
            
            
        #print("query", query)
        #print("params", params)       
        result = await neo4j.execute_write_query(query, params)
        #print("result do salva_mensagem",result)
        
        if not result:
            raise Exception("Falha ao salvar a mensagem no banco de dados")
            
        return mensagem_idx
        
    except Exception as e:
        print(f"Erro ao salvar mensagem: {str(e)}")
        raise

async def carrega_dados_agente(agente_idx: str) -> Optional[Dict[str, Any]]:
    """
    Carrega os dados de um agente do Neo4j usando o agente_idx.
    Retorna apenas os dados do agente em JSON em caso de sucesso.
    """
    query = """
        MATCH (a:Agente {idx: $idx})
        OPTIONAL MATCH (a)-[:USA_LLM_PADRAO]->(llm:LLM)
        RETURN a {.*, llm_padrao: llm.idx, llm_tokens_maximo: llm.tokens_maximo} as a
        """
    parameters = {"idx": agente_idx}
    try:
        # Usa execute_read_query que já trata a execução e retorno
        result = await neo4j.execute_read_query(query, parameters)
       #logger.info(f"Resultado da consulta do agente {agente_idx}: {result}")
        
        # Verifica se encontrou o agente
        if result and len(result) > 0:
            # Extrai apenas as propriedades do nó do agente
            agente_node = result[0].get('a')
            if agente_node:
                # Converte as propriedades do nó para um dicionário Python
                agente_data = _to_str_dict(agente_node)
                #logger.info(f"Dados do agente extraídos: {agente_data}")
                return agente_data
            else:
                logger.warning(f"Nó do agente não encontrado na resposta para idx: {agente_idx}")
                return None
        else:
            logger.warning(f"Nenhum agente encontrado com idx: {agente_idx}")
            return None
            
    except Exception as e:
        logger.error(f"Erro ao carregar dados do agente {agente_idx}: {e}")
        return None

#🚉 Recebe texto enviado pelo usuário
@router.post("/send/text")
async def send_text(data: dict):
    global messageChat  # ✅ USAR A VARIÁVEL GLOBAL
    global tokens_maximo
    print("@@@@@ send_text() =====")
    print(f"data: {data}")
    
     
    # Extrair dados necessários
    mensagem = data.get("mensagem", "")
    modo_resposta_ia = data.get("modo_resposta_ia") or "texto"
    canal_idx = data.get("canal_idx") or "App_web"
    usuario_nome = data.get("usuario_nome", "")
    plataforma_url = data.get("plataforma_url", "")
    agente_idx = data.get("agente_idx")
    usuario_idx = data.get("usuario_idx") or ""
    usuario_whatsapp = data.get("usuario_whatsapp") or ""
    imagem = data.get("imagem") or ""

    print("📩 mensagem", mensagem)
    print("📄🗣️ modo_resposta_ia", modo_resposta_ia)
    print("🧑🏿‍🦰 usuario:", usuario_idx, usuario_nome, usuario_whatsapp)
    print("🌻 imagem", imagem) 
    print("🏭 plataforma_url", plataforma_url)

    #valida se tem mensagem
    if not mensagem:
        return {"success": False, "message": "Mensagem é obrigatória"}

    #valida se tem agente
    if not isinstance(agente_idx, str) or not agente_idx:
        return {"success": False, "message": "agente_idx é obrigatório"}

    #carrega dados do agente
    agente_data = await carrega_dados_agente(agente_idx)
    #print("🤖 agente_data", agente_data)
    if not agente_data:
        return {"success": False, "message": "Agente não encontrado"}


    print("🤖 agente:", agente_idx, agente_data["nome"])
   #🤖carrega o modelo do llm
    if data.get("modelo"):
        modelo = data.get("modelo") 
    else:
        modelo = agente_data.get("llm_padrao") 
        
        
    #modelo = "0987654321"    
        
        
    if data.get("token_maximo"):
        tokens_maximo = data.get("token_maximo")
    else:
        tokens_maximo = agente_data.get("llm_tokens_maximo") or 0
    print("🤖 modelo_idx:",modelo)
    print("tokens_maximo", tokens_maximo)
    modelo = llm.get_model_idx(modelo)
 

    #canal
    canal = await carrega_canal(canal_idx)
    print("📺 canal_idx", canal_idx, canal)



    conta, negocio, conhecimento = await carrega_conta_negocio(usuario_idx,agente_idx)

    print("🏦 conta", conta)
    print("negocio", negocio)

    if not conta:
        return {"success": False, "message": "Você ainda não tem conta neste serviço/app."}

    negocio_idx = None
    if negocio:
        negocio_idx = negocio["idx"]
        print("negocio_idx",negocio_idx)
    
    bc_idx = None
    if conhecimento:
        bc_idx = conhecimento["idx"]
        print("bc_idx",bc_idx)
    
    #🛠️ carrega as ferramentas do agente
    pasta = agente_data.get("pasta")
    print("⚒️ vou carregar as ferramentas", pasta)
    ferramentas = await carrega_ferramentas(pasta)
    #print("ferramentas carregadas",ferramentas)

    #📩 📩📩📩📩📩 Cria ou carrega convesa e mensagens (histórico)
    conversa_idx, historico_mensagens = await cria_ativa_conversa(usuario_idx, agente_idx)

    print("🗣️🗣️🗣️🗣️🗣️ conversa_idx:", conversa_idx)
    #print("📚📚📚📚📚 histórico", historico_mensagens)

    
    #📚 ADICIONAR MENSAGEM DO USUÁRIO AO HISTÓRICO
    if imagem:
        mensagem_completa = f"imagem_link :{imagem};mensagem:{mensagem}"
    else:
        mensagem_completa = mensagem
    
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem_completa, True)

    #📩 salva a mensagem
    mensagem_idx = await salva_mensagem(mensagem_completa, conversa_idx, usuario_idx, agente_idx, "", canal_idx=canal_idx)

    instrucoes = agente_data.get("instrucoes");
    #print("iiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiii instrucoes",instrucoes)
    #return None;

    #🤖 Criar agente
    agent = Agent(
        agente_idx=agente_data.get("idx"),
        agente_nome=agente_data.get("nome"),
        agente_instrucoes=instrucoes,
        agente_identidade=str(agente_data.get("identidade")),
        agente_personalidade=str(agente_data.get("personalidade")),
        agente_estilo_conversa=str(agente_data.get("estilo_conversa")),
        conversa_idx = conversa_idx,
        em_resposta_idx = mensagem_idx,
        modelo=modelo,
        usuario_nome=usuario_nome,
        usuario_idx=usuario_idx,
        usuario_whatsapp=usuario_whatsapp,
        imagem=imagem,
        negocio_idx=negocio_idx,
        conhecimento_idx=bc_idx if bc_idx else None,
        plataforma_url=plataforma_url,
        ferramentas=ferramentas,
        canal_idx=canal_idx,
    )

    #print("agent_instrucoes >>>>>>>>>>>>", agent.instrucoes)
 
    agente_openai = await agent.get_agent_openai()
    #print("agente_openai", agente_openai)
    # ✅ CORRIGIR AS CHAMADAS DAS FUNÇÕES COM OS PARÂMETROS CORRETOS

    print("@@@@@ vou gerar a resposta para o canal:", canal, "modo_resposta_ia:", modo_resposta_ia)

    
    if canal == "App_web":  
        if modo_resposta_ia == "texto": # Resposta pode ter streaming
            print("é App_web e modo texto", "vou chamar resposta_texto_streaming()")
            return await resposta_texto_streaming(
                agente_openai, 
                historico_mensagens, 
                usuario_idx, 
                conversa_idx,
                canal_idx,
                mensagem_idx,
            )
        elif modo_resposta_ia == "audio": # Resposta não pode ter streaming
            print("é App_web e modo audio", "vou chamar resposta_audio()")
            return await resposta_audio(
                agente_openai,
                historico_mensagens,
                usuario_idx,
                agente_idx,  # ← CORRIGIDO: era conversa_idx
                conversa_idx,
                canal_idx,
                mensagem_idx,
                modo_resposta_ia
            )
    elif canal == "Whatsapp": # Resposta não pode ter streaming
        print("é whatsapp e modo audio", "vou chamar resposta_audio()" )
        if modo_resposta_ia == "audio":
            return await resposta_audio(
                agente_openai,
                historico_mensagens,
                usuario_idx,
                agente_idx,  # ← CORRIGIDO: era conversa_idx
                conversa_idx,
                canal_idx,
                mensagem_idx,
                modo_resposta_ia
            )
    
    # Fallback para resposta sem streaming
    resposta = await resposta_texto(
        agente_openai, 
        historico_mensagens, 
        usuario_idx, 
        agente_idx, 
        conversa_idx,
        canal_idx,
        mensagem_idx
    )
    
    # Verificar se alguma função foi acionada
    if resposta.get('funcoes_acionadas'):
        print("\n🔍 FUNÇÕES ACIONADAS NA RESPOSTA:")
        for funcao in resposta['funcoes_acionadas']:
            print(f"- {funcao['nome']}: {funcao.get('argumentos', 'Sem argumentos')}")
    
    return resposta


async def carrega_conta_negocio(usuario_idx,agente_idx):
    query = """
    MATCH (ag:Agente {idx: $agente_idx})-[:ATUA_NO_APP]->(app:App)
    MATCH (c:Conta)-[:ACESSO_AO_APP]->(app)
    MATCH (p:Pessoa {idx: $usuario_idx})-[:POSSUI_CONTA]->(c)
    OPTIONAL MATCH (c)-[:DESTINADA_AO]->(n:Negocio)
    OPTIONAL MATCH (c)-[:ACESSO_AO_CONHECIMENTO]->(bc:Conhecimento)
    RETURN c, n, bc
    """
    #print("===== carrega_conta_negocio() =====")
    #print("usuario_idx", usuario_idx)
    #print("agente_idx", agente_idx)
    params = {
        "usuario_idx": usuario_idx,
        "agente_idx": agente_idx,
    }
    result = await neo4j.execute_read_query(query, params)
    #print("result", result)
    if result and len(result) > 0:
        record = result[0]
        
        # Processar dados da Conta
        conta_node = record.get('c')
        conta_dict = dict(conta_node) if conta_node else None
        
        # Processar dados do Negócio (se existir)
        negocio_node = record.get('n')
        negocio_dict = dict(negocio_node) if negocio_node else None

        # Processar dados do Conhecimento (se existir)
        conhecimento_node = record.get('bc')
        conhecimento_dict = dict(conhecimento_node) if conhecimento_node else None

        return conta_dict, negocio_dict, conhecimento_dict
    
    return None, None, None


def detectar_fase_planejamento(nome_funcao, argumentos):
    """
    Detecta se uma função está na fase de planejamento (coletando dados) ou execução efetiva.
    
    Args:
        nome_funcao (str): Nome da função chamada
        argumentos (dict ou str): Argumentos passados para a função
        
    Returns:
        bool: True se está em fase de planejamento, False se está executando
    """
    print(f"\n🔍 === detectar_fase_planejamento ===")
    print(f"📋 Função: {nome_funcao}")
    print(f"📋 Argumentos: {argumentos}")
    print(f"📋 Tipo dos argumentos: {type(argumentos)}")
    
    # Converte string JSON para dict se necessário
    args_dict = argumentos
    if isinstance(argumentos, str):
        try:
            import json
            args_dict = json.loads(argumentos)
            print(f"✅ Argumentos JSON convertidos para dict: {args_dict}")
        except json.JSONDecodeError as e:
            print(f"❌ Erro ao converter JSON: {e}")
            print(f"⚠️ Argumentos não são dict válido, assumindo execução")
            return False
    
    # Regras específicas para cada função
    if nome_funcao == "entrada_estoque":
        # Se tem o parâmetro adicionar_mais=True, está em fase de planejamento
        if isinstance(args_dict, dict):
            adicionar_mais = args_dict.get('adicionar_mais', False)
            produtos = args_dict.get('produtos', [])
            
            # Se adicionar_mais=True ou produtos está vazio/nulo, está planejando
            if adicionar_mais or not produtos or produtos == []:
                print(f"✅ {nome_funcao} está em FASE DE PLANEJAMENTO")
                print(f"   - adicionar_mais: {adicionar_mais}")
                print(f"   - produtos: {produtos}")
                return True
            else:
                print(f"🔧 {nome_funcao} está EXECUTANDO (tem produtos definidos)")
                return False
        else:
            print(f"⚠️ Argumentos não são dict após conversão, assumindo execução")
            return False
    
    # Para outras funções, analisa padrões gerais de planejamento
    elif isinstance(argumentos, dict):
        # Se tem campos como "coletar_dados", "aguardando_resposta", etc.
        campos_planejamento = ['coletar_dados', 'aguardando_resposta', 'solicitando_info']
        for campo in campos_planejamento:
            if campo in argumentos and argumentos[campo]:
                print(f"✅ {nome_funcao} está em FASE DE PLANEJAMENTO ({campo})")
                return True
    
    print(f"🔧 {nome_funcao} está EXECUTANDO (padrão)")
    return False


def detectar_intencao_por_contexto(resposta_agente, result_completo):
    """
    Detecta a intenção real do agente baseado no contexto completo da conversa
    """
    print(f"\n🔍 === INICIANDO detectar_intencao_por_contexto ===")
    print(f"📝 Resposta do agente: {resposta_agente[:100]}...")
    print(f"📋 Tipo do result_completo: {type(result_completo)}")
    
    deteccoes = []
    
    # Tenta acessar o histórico/contexto da conversa
    contexto_conversa = ""
    if hasattr(result_completo, 'input') and result_completo.input:
        #print(f"✅ result_completo tem input: {type(result_completo.input)}")
        # Extrai mensagens anteriores do input
        if isinstance(result_completo.input, list):
            #print(f"📋 Input é lista com {len(result_completo.input)} elementos")
            for i, msg in enumerate(result_completo.input):
                #print(f"   Msg {i}: {type(msg)} = {msg}")
                if isinstance(msg, dict) and 'content' in msg:
                    contexto_conversa += msg['content'].lower() + " "
                elif hasattr(msg, 'content'):
                    contexto_conversa += str(msg.content).lower() + " "
        elif isinstance(result_completo.input, str):
            #print(f"📋 Input é string: {result_completo.input}")
            contexto_conversa = result_completo.input.lower()
    else:
        #print(f"❌ result_completo não tem input ou input é None")
        #print(f"   Atributos disponíveis: {dir(result_completo)}")
        pass
    #print(f"🧠 Contexto extraído: '{contexto_conversa[:150]}...'")
    #print(f"🧠 Tamanho do contexto: {len(contexto_conversa)} caracteres")
    
    # Mapeamento de intenções baseado no contexto
    intencoes_contexto = {
        'produto_excluir': {
            'palavras_contexto': ['excluir', 'remover', 'deletar', 'apagar'],
            'palavras_resposta': ['código do produto', 'forneça o código', 'informe o código'],
            'peso_contexto': 0.8,
            'peso_resposta': 0.2
        },
        'entrada_estoque': {
            'palavras_contexto': ['entrada', 'adicionar estoque', 'registrar entrada'],
            'palavras_resposta': ['código do produto', 'nome do produto', 'quantidade'],
            'peso_contexto': 0.7,
            'peso_resposta': 0.3
        },
        'gera_cartao_produto': {
            'palavras_contexto': ['mostrar', 'ver', 'listar', 'exibir', 'produtos'],
            'palavras_resposta': ['vou buscar', 'vou mostrar', 'produtos disponíveis'],
            'peso_contexto': 0.6,
            'peso_resposta': 0.4
        },
        'produto_adicionar': {
            'palavras_contexto': ['adicionar produto', 'novo produto', 'criar produto'],
            'palavras_resposta': ['nome do produto', 'dados do produto'],
            'peso_contexto': 0.8,
            'peso_resposta': 0.2
        }
    }
    
    #print(f"\n🔍 === ANALISANDO INTENÇÕES ===")
    for funcao, config in intencoes_contexto.items():
        #print(f"\n🎯 Analisando função: {funcao}")
        score_contexto = 0
        score_resposta = 0
        motivos = []
        
        # Analisa o contexto (mensagens anteriores)
        #print(f"   📋 Palavras do contexto: {config['palavras_contexto']}")
        for palavra in config['palavras_contexto']:
            if palavra in contexto_conversa:
                score_contexto += 1
                motivos.append(f"contexto: '{palavra}'")
                #print(f"   ✅ Encontrou no contexto: '{palavra}'")
            else:
                pass
                #print(f"   ❌ Não encontrou no contexto: '{palavra}'")
        
        # Analisa a resposta atual
        #print(f"   📋 Palavras da resposta: {config['palavras_resposta']}")
        for palavra in config['palavras_resposta']:
            if palavra in resposta_agente:
                score_resposta += 1
                motivos.append(f"resposta: '{palavra}'")
                #print(f"   ✅ Encontrou na resposta: '{palavra}'")
            else:
                pass
                #print(f"   ❌ Não encontrou na resposta: '{palavra}'")
        
        #print(f"   📊 Score contexto: {score_contexto}/{len(config['palavras_contexto'])}")
        #print(f"   📊 Score resposta: {score_resposta}/{len(config['palavras_resposta'])}")
        
        # Calcula confiança ponderada
        if score_contexto > 0 or score_resposta > 0:
            confianca = (
                (score_contexto / len(config['palavras_contexto'])) * config['peso_contexto'] +
                (score_resposta / len(config['palavras_resposta'])) * config['peso_resposta']
            )
            
            #print(f"   🎯 CONFIANÇA CALCULADA: {confianca:.3f}")
            #print(f"   💭 Motivos: {motivos}")
            
            deteccoes.append({
                'funcao': funcao,
                'confianca': confianca,
                'motivo': ' + '.join(motivos),
                'score_contexto': score_contexto,
                'score_resposta': score_resposta
            })
        else:
            pass
            #print(f"   ❌ Nenhum score - função ignorada")
    
    # Ordena por confiança (maior primeiro)
    deteccoes.sort(key=lambda x: x['confianca'], reverse=True)
    
    print(f"\n🎯 === RESULTADO FINAL ===")
    print(f"📊 Total de detecções: {len(deteccoes)}")
    for i, det in enumerate(deteccoes):
        print(f"   {i+1}. {det['funcao']} - Confiança: {det['confianca']:.3f}")
        print(f"      Motivo: {det['motivo']}")
    
    #print(f"🔍 === FIM detectar_intencao_por_contexto ===\n")
    return deteccoes

def extrair_funcoes_acionadas(result):
    """Extrai informações sobre funções acionadas a partir do resultado do agente"""
    funcoes_acionadas = []
    
    # Debug: Mostrar o tipo e atributos do resultado
    #print("\n🔍 DEBUG extrair_funcoes_acionadas")
    #print(f"Tipo do resultado: {type(result)}")
    #print(f"Atributos do resultado: {dir(result)}\n")
    
    # 1. Verifica se o resultado tem atributo 'steps' (comum em execuções de agentes)
    if hasattr(result, 'steps') and result.steps:
        print(f"🔍 Encontrados {len(result.steps)} steps no resultado")
        for i, step in enumerate(result.steps):
            #print(f"  🔍 Step {i}: {type(step)}")
            #print(f"     Atributos: {dir(step)}")
            
            # Verifica tool_calls no step
            if hasattr(step, 'tool_calls') and step.tool_calls:
                print(f"  🔍 Encontradas {len(step.tool_calls)} tool_calls no step {i}")
                for tool_call in step.tool_calls:
                    #print(f"    🔧 Tool call: {tool_call}")
                    #print(f"       Tipo: {type(tool_call)}")
                    #print(f"       Atributos: {dir(tool_call)}")
                    
                    if hasattr(tool_call, 'function') and hasattr(tool_call.function, 'name'):
                        funcoes_acionadas.append({
                            'nome': tool_call.function.name,
                            'argumentos': tool_call.function.arguments,
                            'tipo': 'tool_call',
                            'passo': step.id if hasattr(step, 'id') else i,
                            'fonte': 'steps.tool_calls'
                        })
    
    # 2. Verifica se o resultado tem atributo 'new_items' (forma correta para RunResult)
    if hasattr(result, 'new_items') and result.new_items:
        print(f"🔍 Encontrados {len(result.new_items)} new_items")
        from agents.items import ToolCallItem, ToolCallOutputItem, MessageOutputItem
        
        for i, item in enumerate(result.new_items):
            #print(f" 🔍 Item {i}: {type(item)}")
            #print(f"     Atributos: {dir(item)}")
            
            # Detecta chamadas de ferramentas executadas
            if isinstance(item, ToolCallItem):
                print(f"  🔧 Tool call encontrada: {item.raw_item.name}")
                
                # Verifica se é uma função que está apenas coletando dados (planejamento)
                is_planning = detectar_fase_planejamento(item.raw_item.name, item.raw_item.arguments)
                
                funcoes_acionadas.append({
                    'nome': item.raw_item.name,
                    'argumentos': item.raw_item.arguments,
                    'tipo': 'planned_tool_call' if is_planning else 'tool_call',
                    'passo': f'new_item_{i}',
                    'fonte': 'new_items'
                })
            
            # Detecta saídas de ferramentas
            elif isinstance(item, ToolCallOutputItem):
                print(f"  📤 Tool output encontrada: {item.output}")
                
            # Detecta mensagens do agente (podem conter referências a funções)
            elif isinstance(item, MessageOutputItem):
                print(f"  💬 Mensagem do agente: {type(item.raw_item)}")
                content_text = ""
                
                # Extrai conteúdo de diferentes formatos
                if hasattr(item.raw_item, 'content'):
                    raw_content = item.raw_item.content
                    if isinstance(raw_content, str):
                        content_text = raw_content
                    elif hasattr(raw_content, '__iter__'):
                        # Se for uma lista de ResponseOutputText
                        for content_item in raw_content:
                            if hasattr(content_item, 'text'):
                                content_text += content_item.text + " "
                    else:
                        content_text = str(raw_content)
                
                if content_text:
                    print(f"     Conteúdo extraído: {content_text[:100]}...")
                    content_lower = content_text.lower()
                    
                    # Detecta intenções baseadas no CONTEXTO COMPLETO da conversa
                    intencoes_detectadas = []
                    
                    # Sistema inteligente de detecção por contexto
                    intencao_detectada = detectar_intencao_por_contexto(content_lower, result)
                    
                    if intencao_detectada:
                        for funcao_info in intencao_detectada:
                            funcao = funcao_info['funcao']
                            confianca = funcao_info['confianca']
                            motivo = funcao_info['motivo']
                            
                            if funcao not in intencoes_detectadas and confianca > 0.15:
                                intencoes_detectadas.append(funcao)
                                print(f"  🎯 INTENÇÃO detectada: {funcao} (confiança: {confianca:.2f})")
                                print(f"      💭 Motivo: {motivo}")
                                
                                # Adiciona como função planejada
                                funcoes_acionadas.append({
                                    'nome': funcao,
                                    'argumentos': f'contexto: {motivo}',
                                    'tipo': 'planned_function',
                                    'passo': f'message_item_{i}',
                                    'fonte': 'context_analysis',
                                    'confianca': confianca
                                })
            
            # Outros tipos de itens
            else:
                print(f"  ❓ Item desconhecido: {type(item)}")
                if hasattr(item, 'raw_item'):
                    print(f"     Raw item: {type(item.raw_item)}")
                    if hasattr(item.raw_item, 'content'):
                        print(f"     Conteúdo: {str(item.raw_item.content)[:100]}...")
    
    # 3. Verifica se o resultado é uma resposta de função
    if hasattr(result, 'name') and hasattr(result, 'arguments'):
        print(f"🔍 Encontrada resposta de função: {result.name}")
        funcoes_acionadas.append({
            'nome': result.name,
            'argumentos': result.arguments,
            'tipo': 'function_response',
            'passo': 'final',
            'fonte': 'function_response'
        })
    
    # 4. Verifica se há chamadas de função aninhadas em outros atributos
    for attr_name in dir(result):
        if not attr_name.startswith('_') and attr_name not in ['steps', 'new_items', 'name', 'arguments']:
            attr = getattr(result, attr_name)
            if callable(attr):
                continue
                
            if hasattr(attr, '__dict__'):
                for sub_attr_name, sub_attr in attr.__dict__.items():
                    if hasattr(sub_attr, 'name') and hasattr(sub_attr, 'arguments'):
                        print(f"🔍 Encontrada função em {attr_name}.{sub_attr_name}: {sub_attr.name}")
                        funcoes_acionadas.append({
                            'nome': sub_attr.name,
                            'argumentos': sub_attr.arguments,
                            'tipo': 'nested_function',
                            'passo': f'nested_{attr_name}_{sub_attr_name}',
                            'fonte': f'nested_{attr_name}'
                        })
    
    # 5. Análise simplificada dos raw_responses (apenas para debug)
    if hasattr(result, 'raw_responses') and result.raw_responses:
        print(f"🧠 Analisando {len(result.raw_responses)} raw responses (modo simplificado)...")
        # A detecção de intenções está sendo feita via análise de padrões no MessageOutputItem
        pass
    
    print(f"🔍 Total de funções acionadas encontradas: {len(funcoes_acionadas)}")
    return funcoes_acionadas


async def resposta_audio(agente_openai, historico_mensagens, usuario_idx: str, agente_idx: str, conversa_idx: str, canal_idx: str = "", mensagem_idx:str="", modo_resposta_ia:str=""):
    print("===== resposta_audio()")
    print("modo_resposta_ia", modo_resposta_ia)

    # Inicializar response_body
    response_body = {
        "success": True,
        "modo_resposta_ia": modo_resposta_ia
    }

    try:
        texto_resposta = await resposta_texto(agente_openai, historico_mensagens, usuario_idx, agente_idx, conversa_idx, canal_idx, mensagem_idx, modo_resposta_ia)
        print("texto_resposta", texto_resposta)

        # Verificar se texto_resposta é válido
        if not texto_resposta:
            response_body["success"] = False
            response_body["message"] = "Erro: Não foi possível gerar resposta de texto"
            return JSONResponse(content=response_body, headers={"Content-Type": "application/json"})

        # Converter texto para áudio
        audio_resposta = await text_to_speech(texto_resposta["message"])
        print("tamanho do audio:", len(audio_resposta) if audio_resposta else 0)
        if audio_resposta:
            response_body["resposta_audio"] = audio_resposta
            response_body["audio_format"] = "mp3"
            if modo_resposta_ia == "texto_voz_ao_vivo":
                response_body["resposta_texto"] = texto_resposta
        else:
            response_body["message"] = f"{texto_resposta} (Falha na conversão para áudio)"
            response_body["resposta_texto"] = texto_resposta  # Incluir texto mesmo se áudio falhar
        print("response_body")
        return JSONResponse(content=response_body, headers={"Content-Type": "application/json"})

    except Exception as e:
        print(f"Erro em resposta_audio(): {str(e)}")
        response_body["success"] = False
        response_body["message"] = f"Erro interno: {str(e)}"
        return JSONResponse(content=response_body, headers={"Content-Type": "application/json"})
    
    



async def resposta_texto(agente_openai, historico_mensagens, usuario_idx: str, agente_idx: str, conversa_idx: str, canal_idx: str = "", mensagem_idx:str="",modo_resposta_ia: str=""):
    """Retorna resposta única para Whatsapp (sem streaming)"""
    global messageChat
    resposta_limpa  = ""
    print("========== resposta_texto() ==========")
    try:
        # Usar agent_run_sync para obter resposta completa
        agent_result = await oai.agent_run_sync(agente_openai, historico_mensagens)
        
        # Extrair resposta e resultado completo
        if isinstance(agent_result, dict) and 'response' in agent_result:
            result = agent_result['response']
            full_result = agent_result['full_result']
        else:
            # Fallback para compatibilidade com versão anterior
            result = agent_result
            full_result = None
            
        print("RESPOSTA DO AGENTE:", result)
        print("type(result)", type(result))
        
        # Extrair informações sobre funções acionadas usando o resultado completo
        funcoes_acionadas = []
        if full_result is not None:
            funcoes_acionadas = extrair_funcoes_acionadas(full_result)
        else:
            # Fallback: tentar extrair do resultado simples (pode não funcionar)
            funcoes_acionadas = extrair_funcoes_acionadas(result)
        
        # Classificar funções (sempre disponível, mesmo se lista vazia)
        planejadas_tool_call = [f for f in funcoes_acionadas if f['tipo'] == 'planned_tool_call']
        executadas = [f for f in funcoes_acionadas if f['tipo'] == 'tool_call']  # Apenas funções realmente executadas
        planejadas_pattern = [f for f in funcoes_acionadas if f['tipo'] == 'planned_function']
        mencionadas = [f for f in funcoes_acionadas if f['tipo'] == 'mentioned_function']
        
        # Evita duplicação: se uma função foi detectada como planejada via tool_call, remove da lista de pattern
        nomes_planejadas_tool = [f['nome'] for f in planejadas_tool_call]
        planejadas_pattern = [f for f in planejadas_pattern if f['nome'] not in nomes_planejadas_tool]
        
        # Combina todas as funções planejadas
        planejadas = planejadas_tool_call + planejadas_pattern
        
        if funcoes_acionadas:
            print("\n🔧 FUNÇÕES DETECTADAS:")
            
            if planejadas:
                print("  🎯 FUNÇÕES PLANEJADAS (agente está coletando dados):")
                for i, funcao in enumerate(planejadas, 1):
                    print(f"    {i}. 🧠 {funcao['nome']}")
                    print(f"       Argumentos planejados: {funcao['argumentos']}")
                    print(f"       Status: Coletando dados do usuário (NÃO executada ainda)\n")
            
            if mencionadas:
                print("  💭 FUNÇÕES MENCIONADAS (agente considerou):")
                for i, funcao in enumerate(mencionadas, 1):
                    print(f"    {i}. 💬 {funcao['nome']}")
                    print(f"       Contexto: Mencionada na resposta")
                    print(f"       Fonte: {funcao['fonte']}\n")
            
            if executadas:
                print("  ✅ FUNÇÕES EXECUTADAS:")
                for i, funcao in enumerate(executadas, 1):
                    print(f"    {i}. 🔧 {funcao['nome']}")
                    print(f"       Argumentos: {funcao['argumentos']}")
                    print(f"       Tipo: {funcao['tipo']}")
                    print(f"       Passo: {funcao['passo']}\n")
            
            if not planejadas and not executadas and not mencionadas:
                print("  ❌ Nenhuma função detectada")
        
        print("----------") 
        
        # Capturar nome da função principal executada e planejada
        funcao_executada = executadas[0]['nome'] if executadas else None
        funcao_planejada = planejadas[0]['nome'] if planejadas else None
        
        print(f"🎯 Função executada principal: {funcao_executada}")
        print(f"🧠 Função planejada principal: {funcao_planejada}")
        
        # Log adicional para debug - prioriza EXECUÇÃO sobre planejamento quando há conflito
        if funcao_planejada and funcao_executada and funcao_planejada == funcao_executada:
            # Se a mesma função aparece em ambos, prioriza a EXECUÇÃO (mais importante)
            print(f"📝 STATUS: Agente EXECUTOU {funcao_executada} (processo completo) - PLANEJAMENTO IGNORADO")
            funcao_planejada = None  # Remove do planejamento para evitar confusão
        elif funcao_planejada and not funcao_executada:
            print(f"📝 STATUS: Agente está PLANEJANDO {funcao_planejada} (coletando dados)")
        elif funcao_executada:
            print(f"📝 STATUS: Agente EXECUTOU {funcao_executada} (processo completo)")
        else:
            print(f"📝 STATUS: Nenhuma função detectada")
        
                
        # Verifica se deve retornar resposta vazia (função executada ou em planejamento)
        funcoes_que_retornam_vazio = ["entrada_estoque", "gera_produto_cartao", "cor_lista_cartao"]
        
        print(f"🔍 DEBUG: Verificando retorno vazio:")
        print(f"   - funcao_executada: {funcao_executada}")
        print(f"   - funcao_planejada: {funcao_planejada}")
        print(f"   - funcoes_que_retornam_vazio: {funcoes_que_retornam_vazio}")
        print(f"   - funcao_executada in funcoes_que_retornam_vazio: {funcao_executada in funcoes_que_retornam_vazio}")
        
        if funcao_executada in funcoes_que_retornam_vazio:
            print(f"🚫 Retornando resposta vazia - função EXECUTADA: {funcao_executada}")
            return {
                "success": True,
                "message": "",
            }
        
        # Extrair a resposta preservando o tipo original
        resposta_original = None
        if hasattr(result, 'final_output') and result.final_output:
            resposta_original = result.final_output
        else:
            resposta_original = result
        
        #print("resposta original:", resposta_original)
        #print("tipo da resposta:", type(resposta_original))
        
        # Converter para string para processamento
        resposta_texto = str(resposta_original)
        
        # Verificar se a string tem formato JSON válido
        is_json = False
        json_data = None
        is_number = False
        
        # Checar se resposta_texto contém apenas um número inteiro
        try:
            int(resposta_texto.strip()) # .strip() para remover espaços em branco
            is_number = True
        except (ValueError, TypeError):
            is_number = False
        
        try:
            # Tentar fazer parse da string como JSON
            json_data = json.loads(resposta_texto)
            is_json = True
            print("🆗 JSON VÁLIDO")
            print("----------")
            
            if is_number is False:
                return {
                    "success": True,
                    "message": "",
                }
        except json.JSONDecodeError:

            # se for um json incompleto retornar sem mensagem
            if not resposta_texto or resposta_texto.startswith("{'status':") or resposta_texto.startswith('```json') or 'gera_cartao_produto' in resposta_texto or 'json' in resposta_texto:
                print("🆗 JSON VÁLIDO")
                return {
                    "success": True,
                    "message": "",
                }

            # Verificar se é uma representação de dict válida
            try:
                # Se for uma string que representa um dict (com aspas simples), converter
                if (resposta_texto.strip().startswith('{') and resposta_texto.strip().endswith('}')) or \
                   (resposta_texto.strip().startswith('[') and resposta_texto.strip().endswith(']')):
                    # Substituir aspas simples por aspas duplas para JSON válido
                    json_str = resposta_texto.replace("'", '"')
                    json_data = json.loads(json_str)
                    is_json = True
                    print("@@@@@ DICT/JSON VÁLIDO (convertido de aspas simples)")
                    
                    return {
                        "success": True,
                        "message": resposta_texto,
                        "is_json": True,
                        "data": json_data
                    }
            except (json.JSONDecodeError, ValueError):
                pass
            
            print("❌ JSON INVÁLIDO")
            print("----------")
            is_json = False
            
        except Exception as e:
            print(f":-(@@@@@ ERRO AO PROCESSAR: {e}")
            resposta_texto = str(resposta_original)
            is_json = False

        # ✅ PROCESSAR RESPOSTA E SALVAR NO CACHE/BANCO
        resposta_limpa = limpar_tags_agente(resposta_texto)
        #print("resposta limpa (sem tag do agente):", resposta_limpa)

        # Aplicar limpeza de Markdown para Whatsapp na resposta ao usuário
        if canal.lower() not in ["app_web"]:
            resposta_final =  limpar_markdown_Whatsapp(resposta_limpa)
        else:
            resposta_final = resposta_limpa

        # Lógica condicional baseada no canal para histórico
        if canal.lower() in ["app_web", "app_web"]:
            texto_para_historico = extrair_texto_puro(resposta_limpa)
        else:
            texto_para_historico = limpar_markdown_Whatsapp(resposta_limpa)
        
        await salva_mensagem(
            mensagem = resposta_final, 
            conversa_idx = conversa_idx, 
            usuario_idx = usuario_idx, 
            em_resposta_idx = mensagem_idx,
            agente_idx = agente_idx, 
            remetente = "agente",
            destinatario = "usuario",
            canal_idx = canal_idx,
            )
            
        # Construir resposta com informações sobre funções acionadas
        resposta = {
            "success": True,
            "message": resposta_final if resposta_final else "",
            "is_json": is_json,
            "conversa_idx": conversa_idx,
            "funcoes_acionadas": funcoes_acionadas if funcoes_acionadas else []
        }
        
        # Log da resposta completa (opcional, pode ser removido em produção)
        print("\n📤 RESPOSTA FINAL:")
        print(json.dumps(resposta, indent=2, ensure_ascii=False))
        
        return resposta
        
    except json.JSONDecodeError as e:
        print(f"Erro ao decodificar JSON da resposta: {str(e)}")
        return {
            "success": False,
            "message": "Erro ao processar a resposta do assistente",
            "error": str(e)
        }
    except KeyError as e:
        print(f"Chave não encontrada no JSON de resposta: {str(e)}")
        return {
            "success": False,
            "message": "Formato de resposta inesperado do assistente",
            "error": f"Chave obrigatória não encontrada: {str(e)}"
        }
    except Exception as e:
        print(f"Erro inesperado ao processar resposta: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": "Ocorreu um erro inesperado ao processar a resposta",
            "error": str(e)
        }



@router.post("/evolution_zap_send")
async def evolution_zap_send(data: dict):
    """
    Endpoint para receber dados do webhook Evolution API
    Por enquanto retorna resposta padrão
    """
    logger.info("===== evolution_zap_send() =====")
    #logger.info(f"data recebida: {data}")

    # Usar o identificador de instância correto e aguardar a função assíncrona
    evo_instance_id = data.get("evo_instance_id") or data.get("instanceId")
    conta = await carrega_conta(evo_instance_id)

    data2 = {
        "mensagem": data.get("message") or "",
        "modo_resposta_ia": data.get("modo_resposta_ia") or "texto",
        #"negocio_idx": conta.get("negocio_idx") or "",
        "canal": data.get("canal") or "Whatsapp",
        "usuario_nome": data.get("sender_name") or "",
        "plataforma_url": data.get("plataforma_url") or "",
        "agente_idx": conta.get("agente_idx") or "",
        "usuario_idx": data.get("usuario_idx") or "",
        "evo_instance_id": data.get("evo_instance_id") or "",
        "from": data.get("from") or "",
        "to": data.get("to") or "",
        "canal": "Whatsapp",
        "usuario_whatsapp": data.get("sender_phone") or "",
    }

    # Linha 61-63
    logger.info(f"@@@@@ data2: {data2}")


    return await send_text(data2)

async def carrega_ferramentas(pasta: str) -> list:
    ferramentas = []
    print("🔍 INICIANDO carrega_ferramentas")
    try:
        if pasta:
                # Segunda parte do código (ex: 'atmzap' de 'agent_atmzap')
                
                # Construir o caminho do módulo
                module_path = f"api.agent.{pasta}.function"
                print(f"📁 pasta identificada: {pasta}")
                print(f"🗂️ Caminho do módulo: {module_path}")
                
                try:
                    # Importar o módulo function.py da pasta
                    module = importlib.import_module(module_path)
                    
                    print(f"✅ Módulo {module_path} importado com sucesso")
                    print("📊 Listando todas as funções do módulo...")
                    
                    # Contadores para diagnóstico
                    total_verificadas = 0
                    total_carregadas = 0
                    
                    # Obter todas as funções do módulo que têm o decorator @function_tool
                    for name, obj in inspect.getmembers(module):
                        if name.startswith('_') or name == 'function_tool':
                            #print(f"⏭️ Pulando função: {name} (inicia com _ ou é function_tool)")
                            continue
                        
                        total_verificadas += 1
                        #print(f"\n🔍 Verificando função: {name}")
                        #print(f"   📌 Tipo: {type(obj)}")
                        #print(f"   📌 É função: {inspect.isfunction(obj)}")
                        #print(f"   📌 É FunctionTool: {HAS_NEW_API and isinstance(obj, #FunctionTool)}")
                        #print(f"   📌 Tem __wrapped__: {hasattr(obj, '__wrapped__')}")
                        #print(f"   📌 É callable: {callable(obj)}")
                        
                        # Nova abordagem: usar isinstance para FunctionTool
                        if HAS_NEW_API and isinstance(obj, FunctionTool):
                            ferramentas.append(obj)
                            print(f"   ✅ STATUS: FunctionTool carregada: {name} ✓")
                            total_carregadas += 1
                        elif hasattr(obj, '__wrapped__') and callable(obj):
                            # Compatibilidade com versões antigas
                           
                            ferramentas.append(obj)
                            print(f"   ✅ STATUS: Função carregada (compatibilidade): {name} ✓")
                            total_carregadas += 1
                        elif inspect.isfunction(obj):
                            # Fallback para string matching (método antigo)
                            try:
                                source = inspect.getsource(obj)
                                has_decorator = '@function_tool' in source
                                #print(f"   📌 Tem decorator @function_tool: {has_decorator}")
                                if has_decorator:
                                    ferramentas.append(obj)
                                    print(f"   ✅ STATUS: Função carregada (decorator): {name} ✓")
                                    total_carregadas += 1
                                else:
                                    pass
                                    #print(f"   ❌ STATUS: Rejeitada (sem decorator)")
                            except (OSError, TypeError) as e:
                                print(f"   ❌ STATUS: Erro ao verificar source - {e}")
                        else:
                            pass
                            #print(f"   ❌ STATUS: Rejeitada (tipo inválido)")
                    
                    print(f"\n📈 Total de funções verificadas: {total_verificadas}")
                    print(f"🎯 Total de funções carregadas: {total_carregadas}")
                    #print(f"📋 Funções carregadas: {[f.__name__ if hasattr(f, '__name__') else str(f) for f in ferramentas]}")
                    
                except ImportError as e:
                    print(f"❌ Erro ao importar módulo {module_path}: {e}")
                except Exception as e:
                    print(f"❌ Erro ao carregar funções do módulo {module_path}: {e}")
        else:
            print(f"❌ Necessário informar a pasta do agente', nenhuma função carregada")
            
    except Exception as e:
        print(f"❌ Erro geral ao carregar funções para código '{codigo}': {e}")
    
    #print(f"📊 Lista final de ferramentas: {ferramentas}")

    return ferramentas

if __name__ == "__main__":
    import asyncio 
    import os
    # Substitua a linha 526:
    # os.system('cls')
    
    # Por esta versão mais robusta:
    os.system('cls')


    async def testa_chat():
        data = {
            #"agente_idx": "1508250458", #amk
            #"agente_idx" : "8734235652", #brain
            "agente_idx" : "3482352566",
            #"mensagem": "oi",
            #"mensagem": "me mostre os battons matte",
            #"mensagem": "qual o endereço do cep 31910630",
            "mensagem": "oi, qual seu nome?",
            #"mensagem" : 'nova conversa',
            "modo_resposta_ia": "audio",
            #"modelo": "1234567890",
            "usuario_nome": "",
            "usuario_idx": "1122334455",
            "plataforma_url": "https://www.plataforma.com",
            #"canal_idx": "3245124231",  # whatsapp,
            "canal_idx": "7231564435",  # App_web,
            "usuario_whatsapp": "553184198720"
        }
    
        
        response = await send_text(data)
        print("resposta_testa_chat()", response)
            

    async def carrega_conta_teste():
        conta = await carrega_conta("amzap-3184198720")
        #print(f"conta: {conta}")


    async def evolution_zap_teste():
        data = {
            "evo_instance_id": "amzap-3184198720",
            "message": "Qual é o endereço do CEP 31910630?",
            "modo_resposta_ia": "texto",
            "canal": "Whatsapp",
            "plataforma_url": "https://www.plataforma.com",
            "sender_name": "Glayson Carlos da Silva",
            "sender_phone": "553184198720"
        }


        result = await evolution_zap_send(data)
        print("@@@@ REsultado FIM", result)   
        
    async def testa_encerrar_conversa():
        idx = ""
        usuario_idx = "1029847560"
        agente_idx = "1508250458"
        result = await nova_conversa(conversa_idx="",usuario_idx=usuario_idx, agente_idx=agente_idx)
        print("result do teste", result)
                
    async def testa_carrega_conta_negocio():
        conta,negocio = await carrega_conta_negocio("1122334455","1508250458")
        print("conta",conta)
        print("negocio",negocio)
      
    async def testa_reducao_tokens():
        texto = "Bom dia, tudo bem? Qual o nome do produto 0001?"
        print("texto normal: ", texto)
        texto_compacto = await texto_compactar(texto, max_tokens=0)
        print("texto compactado: ", texto_compacto)
    
    
    #asyncio.run(testa_encerrar_conversa())
    asyncio.run(testa_chat())
    #asyncio.run(testa_reducao_tokens())
    #asyncio.run(carrega_conta_teste())
    #asyncio.run(evolution_zap_teste())
    #asyncio.run(testa_carrega_conta_negocio())







