import os,json,re
from ..agent_neo4j import Agent<PERSON>eo4j
from typing import Optional, List, Dict, Any
from pydantic import BaseModel



#FUNCÇÕES PRÓPRIAS 
#from .functions.produto_catalogo_adicionar import produto_catalogo_adicionar#

from .functions.cliente_adicionar import cliente_adicionar
from .functions.cliente_consultar import cliente_consultar
from .functions.consultar_catalogo_produtos import consultar_catalogo_produtos 
from .functions.cor_lista_cartao import cor_lista_cartao
from .functions.atualiza_cor_produto import atualiza_cor_produto
from .functions.atualiza_produto import atualiza_produto
from .functions.atualiza_cor import atualiza_cor
from .functions.entrada_estoque import entrada_estoque
from .functions.cliente_consultar import cliente_consultar
from .functions.produto_estoque_cartao import produto_estoque_cartao
from .functions.verificar_estoque_revenda import verificar_estoque_revenda 
from .functions.venda_registrar import venda_registrar

#FUNÇÕES DE TERCEIROS
#agent
from .._agent.functions.conversa_nova import conversa_nova

#business
from ..business.functions.produto_variacao_adicionar import produto_variacao_adicionar
from ..business.functions.gera_cartao_produto import gera_cartao_produto
from ..business.functions.cor_adicionar import cor_adicionar

from ..business.functions.produto_excluir import produto_excluir
from ..business.functions.produto_reativar import produto_reativar
from ..business.functions.cor_excluir import cor_excluir

#util
from ..util.functions.busca_endereco_pelo_cep import busca_endereco_pelo_cep


# Inicializa o cliente Neo4j
neo4j = AgentNeo4j()


schema_path = os.path.join(os.path.dirname(__file__),  'schemas', 'completo_neo4j.json')
print("schema_path",    schema_path)



# Cria índices recomendados para melhorar desempenho
async def criar_indices_recomendados():
    """Cria índices recomendados para otimizar as consultas"""
    try:
        # Índice para busca rápida de produtos por negócio e nome
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_negocio_produto_nome IF NOT EXISTS 
            FOR (p:Produto) ON (p.nome)
            """
        )
        
        # Índice para busca rápida de produtos por negócio e código
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_negocio_produto_codigo IF NOT EXISTS 
            FOR (p:Produto) ON (p.codigo)
            """
        )
        
        # Índice para busca rápida de produtos excluídos
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_produto_excluido IF NOT EXISTS 
            FOR (p:Produto) ON (p.excluido)
            """
        )
        
        # Índice para busca rápida de cores por código
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_cor_codigo IF NOT EXISTS 
            FOR (c:Cor) ON (c.codigo)
            """
        )
        
        logger.info("Índices otimizados criados com sucesso")
    except Exception as e:
        logger.error(f"Erro ao criar índices: {str(e)}")

# Executa a criação dos índices ao importar o módulo
import asyncio
#asyncio.create_task(criar_indices_recomendados())

# Importar o esquema JSON do arquivo
with open(os.path.join(os.path.dirname(__file__), 'esquema_neo4j.json'), 'r', encoding='utf-8') as f:
    esquema_json = json.load(f)


neo4j = AgentNeo4j()


