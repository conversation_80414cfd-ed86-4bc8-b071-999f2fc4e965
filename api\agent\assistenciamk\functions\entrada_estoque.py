import logging
from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
from ...util.functions.gera_id_unico import gera_id_unico
from ...agent_evolutionzap import AgentEvolutionZap

# Configuração de logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


import json

try:
    print("Inicializando cliente Neo4j...")
    neo4j = AgentNeo4j()
    print("Cliente Neo4j inicializado com sucesso")
    
    print("Inicializando Evolution API...")
    evolutionApi = AgentEvolutionZap()
    instancia_id = evolutionApi.get_instace_id()
    print(f"Evolution API inicializada. Instância ID: {instancia_id}")
except Exception as e:
    logger.error(f"Erro ao inicializar dependências: {str(e)}")
    raise

from pydantic import BaseModel
from typing import List
import asyncio
from datetime import datetime


class ProdutoItem(BaseModel):
    idx: str
    codigo: str
    nome: str
    preco: float
    quantidade: int

@function_tool
async def entrada_estoque(
    usuario_idx: str,
    usuario_nome: str,
    usuario_whatsapp: str,
    conversa_idx: str,
    negocio_idx: str,
    negocio_nome: str,
    canal_idx: str,
    agente_idx: str,
    em_resposta_idx: str,
    produtos: List[ProdutoItem],
    adicionar_mais: bool = True
):
    """
    Registra uma NOVA ENTRADA DE PRODUTOS no estoque da revenda.
    
    IMPORTANTE: Esta função SEMPRE pergunta ao usuário se deseja adicionar mais produtos
    após processar os produtos informados. Ela não finaliza a sessão de entrada de estoque
    automaticamente - mantém o fluxo ativo até o usuário confirmar que não quer mais produtos.
    
    Comportamento:
    1. Pede ao usuário para informar qual produto deseja adicionar, informando o nome ou código
    2. Busca o produto no catálogo usando consultar_catalogo_produtos() para obter os dados completos
    3. Solicita apenas a quantidade do produto (o preço vem do catálogo)
    4. Adiciona o produto a lista de produtos com preço do catálogo
    5. Repete os passos 1 a 4 enquanto o usuário desejar adicionar novos produtos
    
    🚨IMPORTANTE
    Só para de solicitar produtos quando o usuário responder que não deseja mais adicionar novos
    
    🚨🚨🚨 BUSCA AUTOMÁTICA DE PRODUTOS 🚨🚨🚨
    SEMPRE busque o produto no catálogo usando consultar_catalogo_produtos() para:
    - Obter o preço do produto automaticamente
    - Validar se o produto existe
    - Obter dados completos (nome, código, preço)
    
    Exemplo de busca:
    ```
    query: MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
           WHERE p.excluido <> 1 
           AND (toLower(p.nome) CONTAINS toLower($termo) OR p.codigo CONTAINS $termo)
           RETURN p.idx, p.codigo, p.nome, p.preco
    params: {"negocio_idx": "5544332211", "termo": "ultimate"}
    ```
    
    PROPRIEDADES:
    produtos - Lista com objetos ProdutoItem contendo:
        - idx: identificador único do produto (do catálogo)
        - codigo: código do produto (do catálogo)
        - nome: nome completo do produto (do catálogo)
        - preco: preço do produto (obtido do catálogo automaticamente)
        - quantidade: quantidade comprada (informada pelo usuário)
    Obrigatório
    
    adicionar_mais: boolean
    verificador se o usuário deseja ou não adicionar mais produtos
    default: True
    obrigatório
    
    """
    print("=== entrada_estoque() ===")
    print(f"Usuário: {usuario_nome} ({usuario_idx})")
    print(f"Negócio: {negocio_nome} ({negocio_idx})")
    print(f"Produtos recebidos: {produtos}")


    # Se não há produtos e não é para adicionar mais, retorna erro
    if not produtos and not adicionar_mais:
        error_msg = "Nenhum produto informado para compra"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "function": "entrada_estoque"
        }
        
    # Se for apenas para perguntar se deseja adicionar mais produtos
    if adicionar_mais:
        return {
            "status": "in_progress",
            "message": "Deseja adicionar mais produtos?",
            "function": "entrada_estoque"
        }
    
    
    

    # Normaliza default para evitar lista mutável compartilhada
    if produtos is None:
        produtos = []
        logger.warning("Lista de produtos estava None, foi inicializada como vazia")

    # Gera ids necessários para Revenda (se for criada) e para a Compra
    print("Gerando IDs únicos...")
    try:
        revenda_idx = gera_id_unico()
        compra_idx = gera_id_unico()
        print(f"IDs gerados - Revenda: {revenda_idx}, Compra: {compra_idx}")
        total = 0
    except Exception as e:
        error_msg = f"Erro ao gerar IDs únicos: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "function": "entrada_estoque"
        }

    # Serializa os produtos para dicionários simples e gera idx de ProdutoRevenda por item
    produtos_payload = [
        {
            "idx": p.idx,
            "codigo": p.codigo,
            "nome": p.nome,
            "preco": p.preco,
            "quantidade": p.quantidade,
            "prv_idx": gera_id_unico(),
        }
        
        for p in produtos
    ] if produtos else []

    # Monta a query Cypher para registro de compra
    print("Montando query Cypher...")
    # IMPORTANTE: Esta query NÃO verifica estoque, apenas registra a entrada de produtos
    # Pessoa -[REALIZA_REVENDA]-> Revenda (cria se não existir; define idx no create)
    # Revenda -[COMPROU]-> Compra (Compra com idx e data_hora)
    # Compra -[COMPROU_PRODUTO]-> ProdutoRevenda (para cada item)
    
    # Log dos produtos que serão processados
    print(f"Produtos a serem processados: {json.dumps(produtos_payload, indent=2, ensure_ascii=False, default=str)}")
    
    # Validação dos dados dos produtos
    for produto in produtos_payload:
        if not all(key in produto for key in ["idx", "codigo", "nome", "preco", "quantidade", "prv_idx"]):
            error_msg = f"Dados do produto incompletos: {produto}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
                "function": "entrada_estoque"
            }
    # 
    # REGRA: Não há verificação de estoque durante o registro de compra
    # Query otimizada para registrar compra SEM verificar estoque
    query = """
    // Passo 1: Cria/Atualiza a Revenda e relaciona com Pessoa e Negócio
    MERGE (p:Pessoa {idx: $usuario_idx})
    MERGE (p)-[:REALIZA_REVENDA]->(r:Revenda)
      ON CREATE SET r.idx = $revenda_idx, r.created_at = datetime()

    // Passo 2: Relaciona com o Negócio
    MERGE (n:Negocio {idx: $negocio_idx})
    MERGE (r)-[:REFERENTE]->(n)

    // Passo 3: Calcula o total da compra
    WITH r, $compra_idx AS compra_idx, $produtos AS produtos,
         reduce(total = 0.0, p IN produtos | total + toFloat(p.quantidade) * toFloat(p.preco)) AS total
    
    // Passo 4: Cria o nó da Compra
    CREATE (c:Compra {idx: compra_idx, data_hora: datetime(), total: total})
    MERGE (r)-[:COMPROU]->(c)

    // Passo 5: Processa cada produto da compra
    WITH c, produtos
    UNWIND produtos AS prod
    
    // Passo 6: Encontra o produto no catálogo
    MATCH (pr:Produto {idx: prod.idx})
    
    // Cria ou atualiza o ProdutoRevenda com a nova quantidade
    // IMPORTANTE: Não verifica estoque, apenas adiciona a quantidade informada
    MERGE (r)-[rel:REVENDE]->(prv:ProdutoRevenda {idx: prod.prv_idx})-[:INSTANCIA_DE]->(pr)
    ON CREATE SET 
      // Se não existir, define o estoque como a quantidade da compra
      // Usando COALESCE para garantir que nunca seja NULL
      prv.estoque = coalesce(toInteger(prod.quantidade), 0),
      prv.preco_compra = toFloat(prod.preco),
      prv.created_at = datetime()
    ON MATCH SET 
      // Se existir, incrementa o estoque com a quantidade da compra
      // Usando COALESCE para garantir que valores nulos sejam tratados como 0
      prv.estoque = coalesce(prv.estoque, 0) + coalesce(toInteger(prod.quantidade), 0),
      prv.updated_at = datetime()

    // Cria o relacionamento entre Compra e ProdutoRevenda
    // Sempre adiciona um novo registro de compra, mesmo que seja do mesmo produto
    CREATE (c)-[cp:COMPROU_PRODUTO]->(prv)
    SET cp.quantidade = toInteger(prod.quantidade), 
        cp.preco = toFloat(prod.preco),
        cp.data_hora = datetime()

    RETURN c.idx AS compra_idx, size(produtos) AS total_produtos
    """

    params = {
        "usuario_idx": usuario_idx,
        "negocio_idx": negocio_idx,
        "revenda_idx": revenda_idx,
        "compra_idx": compra_idx,
        "produtos": produtos_payload,
    }

    # Log da query e parâmetros
    print("=== QUERY CYPHER ===")
    print(query)
    print("=== PARÂMETROS ===")
    print(json.dumps(params, indent=2, default=str))
    
    # Validação dos parâmetros
    required_params = ["usuario_idx", "negocio_idx", "revenda_idx", "compra_idx", "produtos"]
    missing_params = [p for p in required_params if p not in params]
    if missing_params:
        error_msg = f"Parâmetros obrigatórios faltando: {', '.join(missing_params)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "function": "entrada_estoque"
        }
    
    
    


    try:
        print("===== EXECUTANDO QUERY NO NEO4J =====")
        
        # Log detalhado dos parâmetros
        param_types = {k: type(v).__name__ for k, v in params.items()}
        print(f"Tipos dos parâmetros: {param_types}")
        
        # Log dos produtos em formato JSON para fácil leitura
        produtos_json = json.dumps(produtos_payload, indent=2, ensure_ascii=False, default=str)
        print(f"Produtos a serem processados:\n{produtos_json}")
        
        # Executa a query no Neo4j
        print("Iniciando execução da query no Neo4j...")
        start_time = datetime.now()
        
        resultado = await neo4j.execute_write_query(query=query, params=params)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        print("===== RESULTADO DA QUERY =====")
        print(f"Tempo de execução: {execution_time:.2f} segundos")
        print(f"Tipo do resultado: {type(resultado).__name__}")
        
        # Log do resultado de forma mais legível
        if resultado:
            if isinstance(resultado, list):
                print(f"Query retornou {len(resultado)} registros")
                for i, item in enumerate(resultado[:5]):  # Limita a 5 itens para não poluir o log
                    print(f"Registro {i + 1}: {item}")
                if len(resultado) > 5:
                    print(f"... mais {len(resultado) - 5} registros não exibidos")
            else:
                print(f"Resultado: {resultado}")
        else:
            logger.warning("A query não retornou resultados (resultado vazio ou None)")
            
        # Verifica se o resultado tem a estrutura esperada
        if not resultado or not isinstance(resultado, list) or len(resultado) == 0:
            error_msg = "A query não retornou os resultados esperados"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
                "function": "entrada_estoque"
            }
        
        # Erro, retorna mensagem de erro.
        if not resultado:
            mensagem = "Ocorreu um erro. A compra não foi finalizada. Nenhum resultado retornado do banco de dados."
            print("ERRO:", mensagem)
            await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem, instancia_id)
            return {
                "status": "error", 
                "message": json.dumps({"tipo":"compra","erro": mensagem}, ensure_ascii=False),
                "function": "entrada_estoque"
            }
        
        
        print("resultado", resultado)
       
        print("@@@ produtos", produtos)
       
        
        # Monta o retorno solicitado
        itens_compra = []
        total_geral = 0.0
        for p in produtos:
            print("@p", p)
            try:
                preco = float(p.preco) if p.preco else 0.0
                quantidade = int(p.quantidade) if p.quantidade else 0
                item_total = preco * quantidade
                total_geral += item_total
                produto_label = f"{p.codigo} - {p.nome}" if getattr(p, "codigo", None) and p.codigo else p.nome
                itens_compra.append({
                    "Produto": produto_label,
                    "Preço": preco,
                    "Qtde": quantidade,
                    "Total": round(item_total, 2),
                })
            except Exception as e:
                logger.error(f"Erro ao processar produto {p}: {str(e)}")
                continue
            
        mensagem_inicial = "Estes são os dados da compra registrada:"
        mensagem_final = ""





        # Enviar mensagens via WhatsApp
        # Data/hora da compra para exibir na mensagem (lado cliente)
        data_compra_str = datetime.now().strftime("%d/%m/%Y %H:%M")

        print('data_compra_str', data_compra_str)


        await enviar_mensagens_zap(
            whatsapp=usuario_whatsapp,
            mensagem_inicial=mensagem_inicial,
            mensagem_final=mensagem_final,
            dados=itens_compra,
            total_geral=round(total_geral, 2),
            data_hora=data_compra_str,
            instancia_id=instancia_id,
            conversa_idx=conversa_idx,
            usuario_idx=usuario_idx,
            usuario_nome=usuario_nome,
            negocio_nome=negocio_nome,
            agente_idx=agente_idx,
            em_resposta_idx=em_resposta_idx,
            canal_idx=canal_idx,
        )

      

        resposta = {
            "status": "success",
            "message": "Compra registrada com sucesso!",
            "data": {
                "compra_idx": compra_idx,
                "total_produtos": len(produtos),
                "total_geral": round(total_geral, 2)
            },
            "function": "entrada_estoque"
        }
        print("@@@@@ Resposta final:", resposta)
        return resposta
    except Exception as e:
        error_msg = f"Ocorreu uma exceção ao tentar registrar a compra de produtos: {str(e)}"
        print("===== ERRO DURANTE A EXECUÇÃO =====")
        print("Tipo do erro:", type(e).__name__)
        print("Mensagem de erro:", str(e))
        print("Traceback:", traceback.format_exc())
        
        # Envia mensagem de erro detalhada apenas para o log
        debug_msg = f"ERRO: {error_msg}\n\nTraceback:\n{traceback.format_exc()}"
        print(debug_msg)
        
        # Envia mensagem genérica para o usuário
        await evolutionApi.send_evolution_message(
            usuario_whatsapp, 
            "Ocorreu um erro ao processar a compra. Por favor, tente novamente.", 
            instancia_id
        )
        
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "debug": debug_msg  # Apenas para depuração, não enviar para o cliente final
        }


async def enviar_mensagens_zap(whatsapp, mensagem_inicial, mensagem_final, dados, total_geral, data_hora, instancia_id, conversa_idx=None, usuario_idx=None, usuario_nome=None,negocio_nome=None, agente_idx=None, em_resposta_idx=None, canal_idx=None):
            
            
            print (f"===== enviar_mensagens()=====")
            print("whatsapp", whatsapp)
            print("mensagem_inicial:" , mensagem_inicial)
            print("mensagem_final:" , mensagem_final)
            print("dados" , dados)
            print("total_geral", total_geral)
            print('data_hora', data_hora)
            print('instancia_id', instancia_id)   
            print('conversa_idx', conversa_idx)
            print("usuario_idx", usuario_idx)
            print("usuario_nome", usuario_nome)
            print("negocio_nome", negocio_nome)
            print("agent_idx", agente_idx)
            print("em_resposta_idx", em_resposta_idx)
            print("canal_idx", canal_idx)
            

            if mensagem_inicial:
                result = await evolutionApi.send_evolution_message(whatsapp, mensagem_inicial, instancia_id)
                          
            
            # Helper para formatar moeda no padrão brasileiro
            def fmt_brl(valor: float) -> str:
                try:
                    return ("R$ " + f"{float(valor):,.2f}").replace(",", "X").replace(".", ",").replace("X", ".")
                except Exception:
                    return f"R$ {valor}"

            # Monta a mensagem no formato solicitado, compatível com WhatsApp Markdown
            linhas = []
            linhas.append(f"*COMPRA EFETUADA EM {data_hora}*")
            if negocio_nome:
                linhas.append(f"*FORNECEDOR:* {negocio_nome}")
            if usuario_nome:
                linhas.append(f"*COMPRADOR:* {usuario_nome}")
            linhas.append("")
            linhas.append("PRODUTOS")

            for item in dados:
                produto = item.get("Produto", "Produto")
                preco = fmt_brl(item.get("Preço", 0))
                qtde = item.get("Qtde", 0)
                total_item = fmt_brl(item.get("Total", 0))
                linhas.append(f"*{produto}*")
                linhas.append(f"Preço: {preco}    Qtde: {qtde}    Total: {total_item}")

            linhas.append("=========================")
            linhas.append(f"*TOTAL: {fmt_brl(total_geral)}*")

            mensagem_whatsapp = "\n".join(linhas)
            
            
            
            
            result = await evolutionApi.send_evolution_message(
                whatsapp,
                mensagem_whatsapp,
                instancia_id
            )
                                    
            await asyncio.sleep(2)

                #if mensagem_final:
                    #message = {}
                    #message["status"] = "success"
                    #message["message"] = mensagem_final
                    #result = await evolutionApi.send_evolution_message(whatsapp,mensagem_final,#instancia_id)
                    #print("📩 mensagem final enviada", result)


            #print(" 📩📩📩📩📩 menagens enviadas")        

            return 




if __name__ == "__main__":

        async def teste_produto_entrada():  
            # Exemplo de uso
            produtos_exemplo = [                                               
            ProdutoItem(idx="10221715", codigo="10221715", nome="Kit Dia + Noite TimeWise®", preco=213.80, quantidade=2),
            ProdutoItem(idx="10221710", codigo="10221710", nome="nome: Kit Básico TimeWise®", preco=182.80, quantidade=2)
            ]

            resultado = await entrada_estoque(
                usuario_idx="1122334455",
                usuario_nome="Carlos Silva",
                usuario_whatsapp="553184198720",
                conversa_idx="42134123",
                negocio_idx="5544332211",
                negocio_nome="Mary Kay",
                canal_idx="245124231",
                agente_idx="3245124231",
                em_resposta_idx="124242323",
                produtos=produtos_exemplo,
                adicionar_mais=False
            )
            print("RESUTADO FINAL\n ",resultado)

        asyncio.run(teste_produto_entrada())
        
        #execução