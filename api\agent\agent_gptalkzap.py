# ==========================================
from shutil import register_archive_format
from fastapi import APIRouter, Request, HTTPException

from api.agent import agent_accountant
from .agent_logger import AgentLogger
from ..cache import cache
from .agent_user import User
from .agent_llm import LLM
from ..functions.util import is_local, generate_unique_id
from .agent_openai import OpenAi
from agents.tool import function_tool
import httpx
from .agent_mysql import AgentMysql  # Adicionado para integrar o agente MySQL
import json
import time
import logging  # ✅ ADICIONADO - Resolve o NameError
import psutil
import uuid
import asyncio
from datetime import datetime
from logging.handlers import RotatingFileHandler
from .agent_neo4j import AgentNeo4j


neo4j = AgentNeo4j()
logger = AgentLogger()
router = APIRouter()
oai = OpenAi()
user = User()
agente_roteador_idx = "gptalkzap"
model = "**********"
# Definir as ferramentas fora da classe
usuario_idx = ""
usuario_whatsapp = ""
usuario_nome = ""
agente_idx = ""
canal_idx = "**********" # whatsapp
gptalkzap_idx = "**********"

def generate_unique_id():
    """Gera um ID único para rastreamento de requisições"""
    return str(uuid.uuid4())


plataforma_url = "https://www.gptalk.com.br"
api_url_base = "https://server.gptalk.com.br/api"

# Definindo a URL completa com protocolo
if is_local():
    plataforma_url = "http://127.0.0.1:8000/gptalk"
    api_url_base = "http://127.0.0.1:8000/api"



@function_tool
async def lista_agentes():
    """
    Lista os agentes disponíveis
    """
    return """
@gizy - Este sou eu.
@maria - Atendente e suporte do app Assistencia MK, que dá suporte as consultoras da Mary Kay.
@tech - Secretaria para oficinas mecânicas ,seja de carros , motos bicicletas ou outros veículos. O app gerenciado por ela é o Oficina Tech.
@mysql - Agente especialista em consultas e operações com banco de dados MySQL.
"""


class AgentGptalkZap:  # PascalCase

    def __init__(self):
        self.instructions = ""
        # Removido self.historico_mensagens para evitar estado compartilhado
        self.intencao = ""
        self.request_counter = 0  # ❌ FALTA
        
    def log_system_metrics(self, request_id: str, stage: str):  # ❌ FALTA TODA FUNÇÃO
        """Log métricas do sistema para diagnóstico"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            logger.info(f"[{request_id}] {stage} - CPU: {cpu_percent}%, RAM: {memory.percent}%, Disk: {disk.percent}%")
            
            if cpu_percent > 80:
                logger.warning(f"[{request_id}] HIGH CPU USAGE: {cpu_percent}%")
            if memory.percent > 85:
                logger.warning(f"[{request_id}] HIGH MEMORY USAGE: {memory.percent}%")
                
        except Exception as e:
            
            logger.error(f"[{request_id}] Erro ao coletar métricas: {e}")

    async def validate_agent(self, nick):
        print("===== validate_agent() =====")
        print("nick", nick)
        nick = nick.lower()
        global agente_idx
        match nick:
            case "@ana" | "@rhiana":
                agente_idx = "2134556436"
                return True             
            case "@gptalkzap" | "@gptalk":
                agente_idx = "**********"
                return True 
            case "@brain":
                agente_idx = "8734235652"
                return True
            case "@mariakarla" | "@amk" | "@mariakenia" | "@maria":
                agente_idx = "1508250458"
                return True
            case "@oficinatech" | "@tech":
                return False
            case "@sofia" | "@brain":
                return False
            case "@neo" | "@neo4j":
                return alse
            case "@servervps" | "@server" | "@vps":
                return True
            case "@clone":
                return False
            case "@atmzap":
                return False

            #case "@mysql":
                #return False
            case _:
                return

    async def welcome_message(self, nick):
        """
        Retorna mensagem de boas-vindas baseada no agente selecionado
        """
        print("===== welcome_message()", nick)
        nick = nick.lower()
        match nick:
            case "@gptalkzap" | "@gptalk":
                return {
                    "status": "success",
                    "message": "" + nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/gptalk/imagens/cardzap.png",
                    "media_type": "image"
                }

            case "@ana" | "@rhiana":
                return {
                    "status": "success",
                    "message": "👷🏿 *RECURSOS HUMANOS* - Atendente: *Ana*",
                    "media_url": "https://gptalk.com.br/app_phj/app/rhiana/imagens/cardzap.png",
                    "media_type": "image"
                }                
                
            case "@mariakarla" | "@mariakenia" | "@maria" | "@assistenciamk":
                return {
                    "status": "success",
                    "message": "ASSISTÊNCIA MARY KAY - Atendente: *Maria*",
                    "media_url": "https://gptalk.com.br/app_phj/app/assistenciamk/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@tech" | "@oficinatech":
                return {
                    "status": "success",
                    "message": "Em que posso ajuda-lo?",
                    "media_url": "https://gptalk.com.br/app_phj/app/oficinatech/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@neo" | "@neo4j":
                return {
                    "status": "success",
                    "message": "",
                    "media_url": "https://gptalk.com.br/app_phj/app/neo/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@clone":
                return {
                    "status": "success",
                    "message": "Sobre nossas cabeças o sol...",
                    "media_url": "https://gptalk.com.br/app_phj/app/clone/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@brain" | "@sofia":
                return {
                    "status": "success",
                    "message": "O que deseja aprender , memorizar ou lembrar?",
                    "media_url": "https://gptalk.com.br/app_phj/app/brain/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@servervps" | "@server" | "@vps":
                return {
                    "status": "success",
                    "message": "Olá, boa noite! Eu sou o agente " + nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/servervps/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@mysql":
                return {
                    "status": "success",
                    "message": "Olá, boa noite! Eu sou o agente " + nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/mysql/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@atmzap":
                return {
                    "status": "success",
                    "message": nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/automarketing/zap/imagens/cardzap.png",
                    "media_type": "image"
                }

            case _:
                return {"status": "success", "message": "Olá! Eu sou " + nick}

    def get_instructions(self, usuario_nome: str, usuario_whatsapp: str):
        self.instructions = f"""
        Você é um agente de atendimento ao cliente da empresa IAPN. Seu nome é Gizy.
        Sua função é esclarecer dúvidas dos usuários sobre o serviço GPTtalk Zap, que dá acesso aos serviços e IAs do sistema GPTalk. 
        No momento você está atendendo o usuário {usuario_nome} com o whatsapp {usuario_whatsapp}.
        Você é educado, divertido , prestativo e responde de forma clara, alegre, mas objetiva.
        """
        return self.instructions

    async def agent_create(self, modelo: str, usuario_nome: str, usuario_whatsapp: str):

        llm = LLM()
        model = llm.get_model_idx(modelo)

        instructions = self.get_instructions(usuario_nome, usuario_whatsapp)

        # Criar lista base de tools (comum para todos os usuários)
        tools_list = [
            lista_agentes,
        ]

        agente = {
            "name": "GPalkZap",
            "instructions": instructions,
            "model": model,
            "tools": tools_list,
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,  # Temporariamente desabilitado devido ao errocom     json_schema
            "input_guardrails": [],
            "output_guardrails": [],
        }

        # Criar o agente
        agente_obj = await oai.agent_create(**agente)

        return agente_obj

    async def detectar_intencao_usuario(self, mensagem: str):
        """
        Detecta a intenção do usuário com base na mensagem
        """
        pass

    def add_message_to_history(self, history: list, message: str, is_user: bool = True) -> list:
        """Adiciona uma mensagem ao histórico no formato padrão de chat"""
        # print("===== add_message_to_history() =====")
        # print("history", history)
        # print("message", message)
        # print("is_user", is_user)
        if not isinstance(history, list):
            history = []

        message_dict = {
            "role": "user" if is_user else "assistant",
            "content": str(message).strip()
        }

        history.append(message_dict)
        return history

    async def get_create_user_talk_cache_key(self, whatsapp: str, agente_atual: str):

        # Verifica se ja existe mensagens no cache deste numero de whatsapp e agente específico
        # Buscar chave específica para este agente e whatsapp
        cache_conversa_key = None
        usuario_idx = None

        # Procurar por chave específica para este agente e whatsapp
        for key in cache.keys():
            if whatsapp in str(key) and agente_atual in str(key):
                cache_conversa_key = key
                # Extrair usuario_idx da chave no formato: conversa_{agente}_{usuario_idx}_{whatsapp}
                parts = cache_conversa_key.split("_")
                if len(parts) >= 4:
                    usuario_idx = parts[2]
                break

        # Se não encontrou cache específico, buscar usuário e criar nova chave
        if not cache_conversa_key:
            usuario = await user.search_phone(whatsapp)

            if not usuario:
                usuario_idx = "0"
            else:
                usuario_idx = usuario["idx"]
            cache_conversa_key = "conversa_" + agente_atual + \
                "_" + usuario_idx + "_" + whatsapp

        return usuario_idx, cache_conversa_key

    async def send_to_agent(
        self,
        mensagem: str,
        agente_atual: str,
        agente_idx: str,
        usuario_idx: str,
        usuario_nome: str,
        usuario_whatsapp: str,
        imagem: str,

 
    ):
        """
        Envia a mensagem para outros agentes
        """
        print("===== gptalkzap_send_to_agent =====")
        print("agente atual", agente_atual)
        start_time = time.time()
        request_id = generate_unique_id()[:8]
        
        logger.info(f"[{request_id}] Iniciando requisição para {agente_atual}")
        
        try:
            data = {
                "usuario_funcao": 1,
                "modelo": "1234567897",  # Corrigido de 123456890 para 1234567890
                "imagem": imagem,
                "usuario_idx": usuario_idx,
                "plataforma_url": plataforma_url,
                "usuario_nome": usuario_nome,
                "modo_resposta_ia": "texto_zap",
                "mensagem": mensagem,
                "agente_idx": agente_idx,
                "chamador": "@gptalkzap",
                "canal_idx": canal_idx, #whatsapp
                "usuario_whatsapp": usuario_whatsapp
            }
            url = api_url_base + "/agent/agent/send/text"

            # Fazendo a requisição HTTP
            print("vou fazer a requisicao")
            # ❌ FALTA - Substituir o httpx.AsyncClient(timeout=30.0) atual por:
            timeout_config = httpx.Timeout(
            connect=15.0,  # Timeout de conexão
            read=120.0,    # Timeout de leitura
            write=30.0,    # Timeout de escrita
            pool=10.0      # Timeout do pool
            )
            
            limits = httpx.Limits(
            max_keepalive_connections=10,
            max_connections=20,
            keepalive_expiry=30.0
            )
            
            async with httpx.AsyncClient(
            timeout=timeout_config,
            limits=limits,
            follow_redirects=True
            ) as client:
                response = await client.post(url, json=data)
                print("@=@= RESPONSE",response)
                response.raise_for_status()  # Levanta exceção se status HTTP for de erro

                # Verificar o tipo de resposta pelo Content-Type
                content_type = response.headers.get("content-type", "")

                if "application/json" in content_type:
                    # Resposta JSON (modos: audio, voz_ao_vivo, texto_voz_ao_vivo)
                    response_data = response.json()
                    #print("response_data", response_data)
                    response_time = time.time() - start_time
                    logger.info(f"[{request_id}] Sucesso para {agente_atual} em {response_time:.2f}s")
                    return response_data
                elif "text/plain" in content_type:
                    # Resposta de texto plano (modo texto normal - streaming)
                    # Para agentes que retornam streaming, precisamos ler todo o conteúdo
                    if agente_atual in ["@neo", "@neo4j"]:
                        # Para Neo4j, ler todo o stream de uma vez
                        response_text = ""
                        async for chunk in response.aiter_text():
                            response_text += chunk
                        response_time = time.time() - start_time
                        logger.info(f"[{request_id}] Sucesso para {agente_atual} em {response_time:.2f}s")
                        return {"status": "success", "message": response_text}
                    else:
                        # Para outros agentes, usar response.text normal
                        response_text = response.text
                        response_time = time.time() - start_time
                        logger.info(f"[{request_id}] Sucesso para {agente_atual} em {response_time:.2f}s")
                        return {"status": "success", "message": response_text}
                else:
                    # Tipo de resposta desconhecido
                    response_text = response.text
                    logger.warning(
                        f"Tipo de resposta desconhecido ({content_type}) de {agente_atual}: {response_text}")
                    response_time = time.time() - start_time
                    logger.info(f"[{request_id}] Sucesso para {agente_atual} em {response_time:.2f}s")
                    return {"status": "success", "message": response_text}

        except httpx.HTTPStatusError as e:
            logger.error(
                f"Erro HTTP ao enviar para {agente_atual}: {e.response.status_code} - {e.response.text}")
            return {"status": "error", "message": f"Erro HTTP: {e.response.status_code}"}

        except httpx.RequestError as e:
            response_time = time.time() - start_time
            logger.error(f"[{request_id}] Erro de conexão para {agente_atual} após {response_time:.2f}s: {str(e)}")
            logger.error(f"[{request_id}] URL: {url}")
            logger.error(f"[{request_id}] Dados: {data}")
            print("XXXXX erro de conexão",e)
            return {"status": "error", "message": "Erro de conexão com o agente" + agente_atual}

        except Exception as e:
            logger.error(
                f"Erro inesperado ao enviar para {agente_atual}: {str(e)}")
            return {"status": "error", "message": f"Erro inesperado: {str(e)}"}

    async def ultima_mensagem(self, whatsapp: str):
        """
        Busca a ultima Mensagem da Pessoa no Canal Whatsapp.
        Devera ser retornado:
        Para qual Agente a Mensagem foi ENVIADO_PARA
        -idx do Agente
        -apelido do Agente
        da Mensagem devera ser retornado:
        -data_hora
        -texto
        da Pessoa evera ser retornado o idx
        idx da Pessoa
        retonar nulo ou vazio se não houve nennhuma mensagem do usuario no canal whataspp
        """
        query = """
        MATCH (p:Pessoa)<-[:ENVIADO_POR]-(m:Mensagem)-[:ENVIADO_PARA]->(a:Agente),
              (m)-[:USANDO_O_CANAL]->(c:Canal)
        WHERE p.whatsapp = $whatsapp 
        RETURN 
            a.idx as agente_idx,
            a.apelido as agente_apelido,
            m.data_hora as data_hora,
            m.texto as texto,
            p.idx as usuario_idx
        ORDER BY m.data_hora DESC
        LIMIT 1
        """
        print("@@@@@ ultima_mensagem()", whatsapp)
        params = {"whatsapp": whatsapp}

        result = await neo4j.execute_read_query(query, params)
        print("result", result)
        return result


    async def obtem_ou_cria_usuario(self, whatsapp, nome):
        print("@@@@@ obtem_ou_cria_usuario()", whatsapp)
        print("vou tentar achar o usuario pelo whatsapp")
        result = await user.search_whatsapp(whatsapp)
        if(result["found"]):
            print("usuario encontrado na busca pelo whatsapp")
            return result["user"]['idx']   
            #print("usuario apos search_whatsapp()", usuario)
        else:
            print("usuario não encontrado na busca pelo whatsapp")

        print("vou tentar achar o usuario pelo telefone normal")
        result  = await user.search_phone(whatsapp)
        print("result do search_phone", result)
        
        if(result["found"]):
            print("usuario encontrado na busca pelo telefone normal")
            await self.seta_numero_whatsapp(whatsapp, result["user"]['idx'])
            return result["user"]['idx']
        else:
            print("usuario não encontrado na busca pelo telefone normal")
        
        print("vou criar o usuario")
        result = await user.add_user_whatsapp(whatsapp,nome)
        print("result add_user_whatsapp", result)
        
        return result['idx']

    async def seta_numero_whatsapp(self, whatsapp, usuario_idx):
        """
        Grava o número do whatsapp no cadastro do usuario (Pessoa) no banco de dados
        """
        query = """
        MATCH (p:Pessoa {idx: $usuario_idx})
        SET p.whatsapp = $whatsapp
        """
        params = {"usuario_idx": usuario_idx, "whatsapp": whatsapp}
        await neo4j.execute_write_query(query, params)

    async def send(self, data: dict):
        """
        Método send para ser chamado por outros agentes
        Agora retorna um array de mensagens ao invés de uma única mensagem
        """
        global agente_idx
        print("@@@@@ gptalkzap_send()")
       
        print("data", data)
        try:

            # Array de mensagens a serem retornadas
            messages_array = []

            # 🎯 OBTER DADOS DO USUÁRIO PRIMEIRO para criar cache específico
            message = data["message"]
            nome = data["sender_name"]
            whatsapp = data["sender_phone"]


            # Validações de entrada
            if not message:
                return {"status": "fail", "messages": [], "error": "Mensagem é obrigatória"}


            if not whatsapp:
                return {"status": "fail", "messages": [], "error": "whatsapp é obrigatório"}

            #carrega agente do cache
            # 🎯 CACHE ESPECÍFICO POR USUÁRIO: usar whatsapp como identificador único
            cache_key_agente = f"agente_atual_{whatsapp}"
            agente_atual = cache.get(cache_key_agente, None)
            if agente_atual:
                print("achei agente no cache")
                print("1-agente_atual", agente_atual)
                agente_existe = await self.validate_agent(agente_atual)

            #carrega agente do cache
            # 🎯 CACHE ESPECÍFICO POR USUÁRIO: usar whatsapp como identificador único
            cache_key_usuario = f"usuario_{whatsapp}"
            usuario_idx = cache.get(cache_key_usuario, None)
            print("1-usuario_idx", usuario_idx)

            if not usuario_idx:
                print("vou verificar se tem mensagem anterior")
                result = await self.ultima_mensagem(whatsapp)       
                #print("##### result do ultimo acesso:", result)
                if result and len(result) > 0:
                    print("#TEVE MENSAGEM ANTERIOR")
                    usuario_idx = result[0]['usuario_idx']
                    agente_idx = result[0]['agente_idx']
                    agente_atual = "@" + result[0]['agente_apelido']
                    cache[cache_key_agente] = agente_atual
                    cache[cache_key_usuario] = usuario_idx
                    print("2-usuario_idx", usuario_idx)
                    print("2-agente_idx", agente_idx)
                    print("2-agente_atual", agente_atual)
                    #registra_acesso_agente(usuario_idx,agente_idx,canal_idx)    
                else:
                    print("#NÃO TEVE MENSAGEM ANTERIOR")
                    if not usuario_idx:
                        # Primeiro precisamos obter o usuario_idx
                        usuario_idx = await self.obtem_ou_cria_usuario(whatsapp,nome)
                        cache[cache_key_usuario] = usuario_idx
                        print("#3-usuario_idx OBTIDO", usuario_idx)


            # Verifica se a mensagem é para a ativação de um agente
            print("se mensagem começar com @, verifica se o nick é valido e atualiza o cache")
            if message.startswith("@"):
                # Divide apenas na primeira ocorrência do espaço
                partes = message.split(" ", 1)
                nick = partes[0]  # Primeira parte é o nome do agente
                texto_adicional = partes[1].strip() if len(
                    partes) > 1 else ""  # Segunda parte é o texto adicional

                agente_existe = await self.validate_agent(nick)
                if agente_existe:
                    # 15 minutos com TTL global - específico por usuário
                    cache[cache_key_agente] = nick
                    welcome_msg = await self.welcome_message(nick)
                    # Adicionar mensagem de boas-vindas ao array
                    messages_array.append(welcome_msg)

                    # Se não há texto adicional, retorna apenas a mensagem de boas-vindas
                    if not texto_adicional:
                        return {"status": "success", "messages": messages_array}

                    # Se há texto adicional, atualiza a mensagem e continua o fluxo
                    message = texto_adicional
                    agente_atual = nick  # Atualiza o agente_atual para o fluxo continuar
                else:
                    error_msg = {
                        "status": "fail",
                        "message": "Não foi possível identificar a IA. Por favor, verifique se o nome está correto. Você pode informar também o nome do app. Exemplo: @oficinatech",
                        "media_url": None,
                        "media_type": None
                    }
                    messages_array.append(error_msg)

            # Se não houver nenhum agente atual, setar como '@gptalkzap' e adicionar mensagem de boas-vindas
            if not agente_atual:
                print("nao tem agente atual")
                agente_atual = "@gptalkzap"
                agente_idx = gptalkzap_idx
                print("agora o agente atual é o ", agente_atual, agente_idx)
                # 15 minutos com TTL global - específico por usuário
                cache[cache_key_agente] = agente_atual

                # Adicionar mensagem de boas-vindas ao array
                welcome_msg = await self.welcome_message(agente_atual)
                messages_array.append(welcome_msg)



            # Obter imagem dos dados se disponível
            imagem = data.get("image", "")

            response = await self.send_to_agent(
                    mensagem=message,
                    agente_atual=agente_atual,
                    agente_idx=agente_idx,
                    usuario_idx=usuario_idx,
                    usuario_nome=nome,
                    usuario_whatsapp=whatsapp,
                    imagem=imagem,
                           
            )
                
                
            print("resposta do send_to_agent", response)
            
            result_json = response["message"]
            if isinstance(result_json, str):
                messages_array.append(response)
                return {"status": "success", "messages": messages_array}
            else:
                return {"status": "success", "messages": messages_array}
                    
                    
            

        except Exception as e:
            logger.error(f"Erro no método send do AgentGptalkZap: {str(e)}")
            raise



    def renomear_cache_conversa(self, old_key: str, new_key: str) -> bool:
        """
        Renomeia uma chave de conversa no cache

        Args:
            old_key (str): Chave antiga (ex: conversa_gptalkzap_0_553184198720)
            new_key (str): Nova chave (ex: conversa_gptalkzap_1234567890_553184198720)

        Returns:
            bool: True se a renomeação foi bem-sucedida, False caso contrário
        """
        try:
            if old_key in cache:
                # Move o histórico para a nova chave
                cache[new_key] = cache.pop(old_key)
                return True
            else:
                logger.warning(f"Chave {old_key} não encontrada no cache")
                return False
        except Exception as e:
            logger.error(f"Erro ao renomear cache: {str(e)}")
            return False
        
        
    async def carrega_cartao_mensagens (self,fonte):
            print("##### carrega_cartao_mensagens #####")
            
            result_json = fonte["message"]
            if isinstance(result_json, str):
                result_json = json.loads(result_json)
            else:
                return  [] #retorna vazio
            mensagem_inicial = result_json.get("mensagem_inicial", "")
            mensagem_final = result_json.get("mensagem_final", "")
            
            messages = []
            if mensagem_inicial:
                message = {}
                message["status"] = "success"
                message["message"] = mensagem_inicial
                messages.append(message)
            
             
            # Processar cada produto do resultado
            if 'dados' in result_json and isinstance(result_json['dados'], list):
                for item in result_json['dados']:
                    message = {}
                    message["status"] = "success"
                    
                    # Construir a mensagem formatada
                    mensagem_formatada = f"*{item.get('nome', 'Produto sem nome')}*\n"
                    
                    # Adicionar preço se disponível
                    if 'preco' in item and item['preco'] is not None:
                        mensagem_formatada += f"💵 *Preço:* R$ {item['preco']:.2f}\n"
                    
                    # Adicionar variações se disponíveis
                    if 'variacoes' in item and isinstance(item['variacoes'], list) and len(item['variacoes']) > 0:
                        mensagem_formatada += "\n*Cores disponíveis:*\n"
                        for variacao in item['variacoes']:
                            cor = variacao.get('cor', 'Cor não especificada')
                            codigo = variacao.get('codigo', '')
                            mensagem_formatada += f"• {cor}"
                            if codigo:
                                mensagem_formatada += f" (Código: {codigo})"
                            mensagem_formatada += "\n"
                    
                    message["message"] = mensagem_formatada.strip()
                    
                    # Adicionar URL da mídia se disponível
                    if 'url_imagem' in item and item['url_imagem']:
                        message["media_url"] = item['url_imagem']
                        message["media_type"] = "image"
                    
                    messages.append(message)
            
            
            if mensagem_final:
                message = {}
                message["status"] = "success"
                message["message"] = mensagem_final
                messages.append(message)
  
            # Imprimir o array de mensagens para depuração
            print("Mensagens formatadas:", json.dumps(messages, indent=2, ensure_ascii=False))
            return messages
            
            
            
            
            
            

    def listar_chaves_cache(self, filtro: str = None) -> list:
        """
        Lista todas as chaves do cache, opcionalmente com filtro

        Args:
            filtro (str): Filtro opcional para as chaves (ex: "conversa_", whatsapp específico)

        Returns:
            list: Lista de chaves do cache
        """
        try:
            if filtro:
                return [key for key in cache.keys() if filtro in str(key)]
            return list(cache.keys())
        except Exception as e:
            logger.error(f"Erro ao listar chaves do cache: {str(e)}")
            return []

    def deletar_cache_conversa(self, key: str) -> bool:
        """
        Deleta uma conversa específica do cache

        Args:
            key (str): Chave da conversa a ser deletada

        Returns:
            bool: True se deletado com sucesso, False caso contrário
        """
        try:
            if key in cache:
                del cache[key]
                return True
            else:
                logger.warning(f"Chave {key} não encontrada no cache")
                return False
        except Exception as e:
            logger.error(f"Erro ao deletar cache: {str(e)}")
            return False



# ❌ FALTA TODA A CLASSE
class ConnectionMonitor:

    def __init__(self):
        self.error_history = []
        self.success_history = []
        self.max_history = 100
    
    def record_error(self, request_id: str, error_type: str, duration: float, system_metrics: dict):
        # ❌ FALTA - Atualmente só tem comentário "# ... implementação completa ..."
        """Registra erro para análise de padrões"""
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'request_id': request_id,
            'error_type': error_type,
            'duration': duration,
            'cpu_percent': system_metrics.get('cpu', 0),
            'memory_percent': system_metrics.get('memory', 0),
            'hour': datetime.now().hour
        }
        
        self.error_history.append(error_record)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        
        # Análise de padrões
        self.analyze_patterns()
    
    def analyze_patterns(self):
        # ❌ FALTA - Atualmente só tem comentário "# ... análise de padrões ..."
        """Analisa padrões nos erros"""
        if len(self.error_history) < 5:
            return
        
        recent_errors = self.error_history[-10:]
        
        # Padrão de horário
        hours = [e['hour'] for e in recent_errors]
        if len(set(hours)) <= 2:
            logger.warning(f"PADRÃO DETECTADO: Erros concentrados nos horários {set(hours)}")
        
        # Padrão de CPU alta
        high_cpu_errors = [e for e in recent_errors if e['cpu_percent'] > 80]
        if len(high_cpu_errors) > 5:
            logger.warning(f"PADRÃO DETECTADO: {len(high_cpu_errors)} erros com CPU alta")
        
        # Padrão de memória alta
        high_memory_errors = [e for e in recent_errors if e['memory_percent'] > 85]
        if len(high_memory_errors) > 5:
            logger.warning(f"PADRÃO DETECTADO: {len(high_memory_errors)} erros com memória alta")

# Instância global
connection_logger = logging.getLogger('agent_connection')
file_handler = RotatingFileHandler('logs/agent_connections.log', maxBytes=10*1024*1024, backupCount=5)
connection_monitor = ConnectionMonitor()  # ❌ FALTA


if __name__ == "__main__":
    import asyncio

    async def main():
        agent = AgentGptalkZap()
        result = await agent.send({
            "message": "@vps", 
            "modelo": "1234567897", 
            "sender_name": "João",
             "sender_phone": "31984198720", 
             "image": "https://www.gptalkzap.com.br/images/logo.png"}
             )
        print(result)
    


    async def test_send_to_agent_atmzap():
        data = {
            "agente_atual": "@atmzap",
            "usuario_idx": "1122334455",
            "usuario_nome": "Carlos",
            "usuario_whatsapp": "553184198720",
            "sender_name": "Carlos",
            "sender_phone": "553184198720",
            "mensagem": "como faço para conectar minha conta no Whatsapp",
            "imagem" : ""
        }


        result = await gzap.send_to_agent(**data)
        print(result)

    async def test_ultima_mensagem():
        #pessoa existe, mas sem ultimo acesso
        data = {
            "message": "olá, bom dia",
            "sender_name": "Carlos",
            "sender_phone": "553184198720",
            "imagem" : ""
        }
        gzap = AgentGptalkZap()
        result = await gzap.send(data)    
        #print("resultado",result)





    async def test_send_to_agent_assistenciamk2():
        data = {
            "message": "@maria liste os battons matte",
            "nome": "Carlos",
            "whatsapp": "3184198720",
            "imagem" : ""
        }

        gzap = AgentGptalkZap()
        result = await gzap.send(data)    
        print("resultado",result)
    
    async def test_send_to_agent_assistenciamk():

        data = {
            "agente_atual": "@maria",
            "usuario_idx": "1122334455",
            "usuario_nome": "Carlos",
            "usuario_whatsapp": "553184198720",
            #"mensagem": "qual o seu nome",
            "mensagem": "liste os batons matte",
            "imagem" : ""
        }

        gzap = AgentGptalkZap()
        result = await gzap.send_to_agent(**data)
        print("result", result)
        try:
            #print(result)
            result_json = result["message"]
            if isinstance(result_json, str):
                result_json = json.loads(result_json)
            mensagem_inicial = result_json.get("mensagem_inicial", "")
            mensagem_final = result_json.get("mensagem_final", "")
            
            messages = []
            if mensagem_inicial:
                message = {}
                message["status"] = "success"
                message["message"] = mensagem_inicial
                messages.append(message)
            
             
            # Processar cada produto do resultado
            if 'dados' in result_json and isinstance(result_json['dados'], list):
                for item in result_json['dados']:
                    message = {}
                    message["status"] = "success"
                    
                    # Construir a mensagem formatada
                    mensagem_formatada = f"*{item.get('nome', 'Produto sem nome')}*\n"
                    
                    # Adicionar preço se disponível
                    if 'preco' in item and item['preco'] is not None:
                        mensagem_formatada += f"💵 *Preço:* R$ {item['preco']:.2f}\n"
                    
                    # Adicionar variações se disponíveis
                    if 'variacoes' in item and isinstance(item['variacoes'], list) and len(item['variacoes']) > 0:
                        mensagem_formatada += "\n*Cores disponíveis:*\n"
                        for variacao in item['variacoes']:
                            cor = variacao.get('cor', 'Cor não especificada')
                            codigo = variacao.get('codigo', '')
                            mensagem_formatada += f"• {cor}"
                            if codigo:
                                mensagem_formatada += f" (Código: {codigo})"
                            mensagem_formatada += "\n"
                    
                    message["message"] = mensagem_formatada.strip()
                    
                    # Adicionar URL da mídia se disponível
                    if 'url_imagem' in item and item['url_imagem']:
                        message["media_url"] = item['url_imagem']
                        message["media_type"] = "image"
                    
                    messages.append(message)
            
            
            if mensagem_final:
                message = {}
                message["status"] = "success"
                message["message"] = mensagem_final
                messages.append(message)
  
            # Imprimir o array de mensagens para depuração
            print("Mensagens formatadas:", json.dumps(messages, indent=2, ensure_ascii=False))
            
            
            
            
        except json.JSONDecodeError as e:
            print(f"Erro ao decodificar string JSON: {str(e)}")
            print(result)
        else:
            pass
            #print(result_json)
        
        
        

    async def test_send_to_agent_oficinatech():

        data = {
            "agente_atual": "@tech",
            "usuario_idx": "1122334455",
            "usuario_nome": "Carlos",
            "usuario_whatsapp": "553184198720",
            "mensagem": "oooiiiiii bom dia",
            "imagem" : ""
        }

        gzap = AgentGptalkZap()
        result = await gzap.send_to_agent(**data)
        #print(result)
    
    asyncio.run(main())
    #asyncio.run(test_send_to_agent_atmzap())
    #asyncio.run(test_send_to_agent_assistenciamk())
    #asyncio.run(test_send_to_agent_oficinatech())
    #asyncio.run(test_ultima_mensagem())
    
    #Execucação:
    #py -m api.agent.agent_gptalkzap

