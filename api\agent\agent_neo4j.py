from pydantic import BaseModel, <PERSON> 
from typing import List, Literal, Optional 
from fastapi import APIRouter
from fastapi.responses import StreamingResponse, JSONResponse
from .agent_llm import LLM
from ..cache import cache
from ..functions.util import generate_unique_id
from .agent_openai import OpenAi
from agents import function_tool
from datetime import datetime
import pytz
from .agent_logger import Agent<PERSON>ogger
from .agent_message import Message
from .agent_secret import Secret
from threading import Lock
import json
from neo4j_graphrag.embeddings import OpenAIEmbeddings
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.llm import LLMInterface, OpenAILLM, LLMResponse
from neo4j import GraphDatabase


logger = AgentLogger()
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()
secret = Secret()

router = APIRouter()
oai = OpenAi()


class AgentNeo4j:
    def __init__(
        self,
        name="neo4j",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None):
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        database=None
        user=None
        password=None
        uri=None

        # 📊 LOGS DETALHADOS DE CONEXÃO

        self.name = name
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.negocio_idx = negocio_idx

        self.uri = uri if uri else secret.GPTALK_01_NEO4J_URI
        self.user = user if user else secret.GPTALK_01_NEO4J_USER
        self.password = password if password else secret.GPTALK_01_NEO4J_PASSWORD
        self.database = database if database else secret.GPTALK_01_NEO4J_DATABASE
       
        self.driver = None        
        
        self.instructions = f"""
        Você é um assistente especializado em Neo4j que ajuda a converter consultas em linguagem natural 
        para queries Cypher e executá-las no banco de dados de grafos.

        Suas principais responsabilidades:
        1. **Interpretar consultas em linguagem natural** e convertê-las para Cypher
        2. **Executar queries Cypher** no banco Neo4j usando as ferramentas apropriadas
        3. **Consultar o schema** do banco quando necessário para entender a estrutura
        4. **Apresentar resultados** de forma clara e organizada


        ## 🛠️ **Ferramentas Disponíveis:**

        ### `consultar_schema_neo4j()`
        - Use para entender a estrutura do banco (nós, relacionamentos, propriedades)
        - Consulta o schema definido no arquivo modelo_dados.py (formato JSON)
        - Sempre consulte o schema antes de criar queries complexas ou criar novos nós, consultar nós , relacionamentos e propriedades.
        - Essencial para queries que envolvem múltiplos tipos de nós

        ### `gera_idx()`
        - **FUNÇÃO OBRIGATÓRIA:** Gera identificador único (IDX) para novos nós
        - Use SEMPRE antes de criar novos nós com CREATE ou MERGE
        - Retorna um ID único de 10 dígitos baseado em timestamp + número aleatório
        - **REGRA:** Todo nó criado DEVE ter a propriedade `idx` com valor único

        ### `executar_cypher_leitura(query, params)`
        - Para queries de **consulta** (MATCH, RETURN, WHERE, ORDER BY, LIMIT)
        - Use quando precisar buscar ou listar dados
        - Exemplos: buscar usuários, listar relacionamentos, estatísticas

        ### `executar_cypher_escrita(query, params)`
        - Para queries de **modificação** (CREATE, MERGE, SET, DELETE, REMOVE)
        - Use quando precisar criar, atualizar ou remover dados
        - **CUIDADO:** Estas operações modificam permanentemente o banco
        - **REGRA OBRIGATÓRIA:** Sempre use gera_idx() e adicione propriedade `idx` em novos nós

        ### 🧩 **APOC Plugin Disponível**
        - O plugin **APOC** está instalado e disponível no banco Neo4j.
        - Você pode utilizar qualquer função, procedimento ou utilitário do APOC quando necessário para enriquecer, transformar ou facilitar queries Cypher.
        - Exemplos de uso: manipulação de listas, datas, conversão de tipos, chamadas HTTP, importação/exportação, utilitários de grafos, etc.
        - Consulte a [documentação oficial do APOC](https://neo4j.com/labs/apoc/4.4/) para detalhes e exemplos de uso.

        ## 📋 **Processo de Trabalho:**

        1. **Analise** a solicitação do usuário
        2. **Consulte o schema** se necessário (especialmente para queries complexas)
        3. **Para criação de nós:** SEMPRE use gera_idx() primeiro para obter IDX único
        4. **Converta** a linguagem natural para Cypher apropriado (incluindo idx nos nós)
        5. **Execute** a query usando a ferramenta correta (leitura vs escrita)
        6. **Apresente** os resultados de forma clara

        ## ⚠️ **Diretrizes Importantes:**

        - **OBRIGATÓRIO:** Todo nó criado deve ter propriedade `idx` com valor único
        - **SEMPRE** use gera_idx() antes de CREATE ou MERGE de novos nós
        - **Sempre** valide a sintaxe Cypher antes de executar
        - **Prefira** consultar o schema para queries desconhecidas
        - **Use parâmetros** nas queries quando apropriado para segurança
        - **Explique** o que cada query faz antes de executá-la
        - **Seja cuidadoso** com operações de escrita - elas são permanentes

        ## 🚨 **Exemplo de Criação de Nó Correto:**
        ```
        1. Primeiro: gera_idx() → retorna idx: "1234567890"
        2. Depois: CREATE (n:Usuario {{"idx": "1234567890", "nome": "João", "email": "<EMAIL>"}})
        ```

        ## 👤 **Contexto da Sessão:**
        - Usuário: {usuario_nome}
        - Usuario_idx: {usuario_idx}
        - Data/Hora: {data_hora}
        - Negócio: {negocio_idx}

        O usuário é uma Pessoa no banco de dados.  E o usuario_idx corresponde a proopriedade idx da Pessoa. Desta forma, qualquer pesquisa ou inserção de dados do usuario no banco de dados deve-se levar em consideracao a Pessoa que o representa. 

        Responda sempre em português brasileiro e seja didático nas explicações.
        """

    def get_instructions(self):
        return self.instructions


    async def conecta(self):
        if not self.driver:
            self.driver = GraphDatabase.driver(
                self.uri,
                auth=(self.user, self.password)
            )
        return self.driver

    async def close(self):
        if self.driver:
            self.driver.close()
            self.driver = None





    async def execute_read_query(self, query: str, params: dict = None):
        """
        Executa uma query Cypher de LEITURA no banco Neo4j REAL
        
        Args:
            query: String contendo a consulta Cypher
            params: Dicionário com os parâmetros da consulta
            
        Returns:
            list: Lista de dicionários com os resultados da consulta
        """
        import time
        import logging
        from datetime import datetime
        #print("========== execute_read_query() ==========")
        #print("query", query)
        #print("params", params)
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            filename='neo4j_query_debug.log',
            filemode='a'  # append mode
        )
        
        # Log de início da execução
        start_time = time.time()
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        #print(f"\n🔵 [{timestamp}] Iniciando execute_read_query")
        #print(f"🔵 Query: {query}")
        #print(f"🔵 Parâmetros: {params}")
        
        
        try:
            from ..functions.mcp_functions import mcp_neo4j_read_neo4j_cypher
            
            # Garante que os parâmetros sejam um dicionário
            params = params or {}
            
            
            # Executa a consulta
            resultado = mcp_neo4j_read_neo4j_cypher(query, params)
            
            # Cálculo do tempo de execução
            execution_time = time.time() - start_time
            
            # Log do resultado
            result_type = type(resultado).__name__
            result_length = len(resultado) if hasattr(resultado, '__len__') else 'N/A'
            
            
            #if resultado and len(resultado) > 0:
             #   print(f"📊 Primeiro item do resultado: {resultado[0]}")
            
            # Log detalhado
            
            #if resultado and len(resultado) > 0:
                #logging.info(f"Amostra do resultado: {resultado[0]}")
            
            return resultado
            
        except Exception as e:
            import traceback
            execution_time = time.time() - start_time
            error_msg = f"❌ [Neo4j] Erro após {execution_time:.2f}s: {str(e)}"
            
            print(error_msg)
            print(f"🔍 [Neo4j] Traceback: {traceback.format_exc()}")
            
            # Log detalhado do erro
            logging.error(f"Erro após {execution_time:.2f}s: {str(e)}")
            logging.error(f"Tipo do erro: {type(e).__name__}")
            logging.error(f"Mensagem de erro: {str(e)}")
            logging.error(f"Query: {query}")
            logging.error(f"Parâmetros: {params}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            
            # Retorna uma lista vazia em caso de erro para evitar quebrar o fluxo
            return []


    async def execute_write_query(self, query: str, params: dict = None):
        """
        Executa uma query Cypher de ESCRITA no banco Neo4j REAL
        Este método é usado para operações que modificam dados
        """
        import time
        import traceback
        from datetime import datetime
        
        # Configuração inicial
        start_time = time.time()
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        logger.info(f"\n🔵 [{timestamp}] ===== INÍCIO execute_write_query =====")
        logger.info(f"🔵 Query: {query}")
        logger.info(f"🔵 Parâmetros: {params}")
        
        try:
            # Validação da query
            if not query or not isinstance(query, str) or not query.strip():
                error_msg = "Query inválida: não pode ser vazia"
                logger.error(f"❌ {error_msg}")
                return {"erro": error_msg}
            
            # Validar se é realmente uma query de escrita
            write_keywords = ["CREATE", "MERGE", "SET", "DELETE", "REMOVE", "DROP"]
            if not any(keyword in query.upper() for keyword in write_keywords):
                error_msg = "Query de escrita deve usar CREATE, MERGE, SET, DELETE, REMOVE ou DROP"
                logger.error(f"❌ {error_msg}")
                return {"erro": error_msg}
            
            # Validar parâmetros
            if params is None:
                params = {}
                logger.warning("⚠️  Parâmetros não fornecidos, usando dicionário vazio")
            
            logger.info(f"🔵 Tipos dos parâmetros: { {k: type(v).__name__ for k, v in params.items()} }")
            
            # Verificar se há listas grandes nos parâmetros para não sobrecarregar o log
            for key, value in params.items():
                if isinstance(value, (list, dict)) and len(str(value)) > 100:
                    logger.info(f"🔵 Parâmetro '{key}': {type(value).__name__} com {len(value) if hasattr(value, '__len__') else 'N/A'} itens")
                else:
                    logger.info(f"🔵 {key}: {value}")
            
            # Importar a função MCP apenas quando necessário
            logger.info("🔵 Importando mcp_neo4j_write_neo4j_cypher...")
            try:
                from ..functions.mcp_functions import mcp_neo4j_write_neo4j_cypher
                logger.info("✅ mcp_neo4j_write_neo4j_cypher importado com sucesso")
            except ImportError as ie:
                error_msg = f"❌ Erro ao importar mcp_neo4j_write_neo4j_cypher: {str(ie)}"
                logger.error(error_msg)
                logger.error(f"❌ Traceback: {traceback.format_exc()}")
                return {"erro": error_msg}
            
            # Executar query de escrita usando a função MCP real
            logger.info("🔵 Executando mcp_neo4j_write_neo4j_cypher...")
            try:
                resultado = mcp_neo4j_write_neo4j_cypher(query, params)
                
                # Log do resultado
                execution_time = time.time() - start_time
                logger.info(f"✅ Query executada com sucesso em {execution_time:.2f} segundos")
                
                if resultado is None:
                    logger.warning("⚠️  A função retornou None")
                elif isinstance(resultado, dict) and 'erro' in resultado:
                    logger.error(f"❌ Erro na execução: {resultado.get('erro')}")
                else:
                    logger.info(f"✅ Resultado: {resultado}")
                
                return resultado
                
            except Exception as e:
                execution_time = time.time() - start_time
                error_msg = f"❌ Erro durante a execução da query após {execution_time:.2f}s: {str(e)}"
                logger.error(error_msg)
                logger.error(f"❌ Traceback: {traceback.format_exc()}")
                logger.error(f"❌ Query: {query}")
                logger.error(f"❌ Parâmetros: {params}")
                
                return {
                    "erro": f"Erro na execução da query: {str(e)}",
                    "tipo_erro": type(e).__name__,
                    "traceback": traceback.format_exc(),
                    "query": query,
                    "params": params
                }
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"❌ Erro geral em execute_write_query após {execution_time:.2f}s: {str(e)}"
            logger.error(error_msg)
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            
            return {
                "erro": f"Erro geral: {str(e)}",
                "tipo_erro": type(e).__name__,
                "traceback": traceback.format_exc(),
                "query": query,
                "params": params
            }
        finally:
            execution_time = time.time() - start_time
            logger.info(f"🟢 Tempo total de execução: {execution_time:.2f} segundos")
            logger.info(f"🔵 ===== FIM execute_write_query =====\n")




# ===============================================================================
# FUNÇÕES DE FERRAMENTAS (TOOLS) PARA O AGENTE NEO4J
# ===============================================================================


async def consultar_schema_neo4j():
    """
    Consulta e retorna o schema do banco de dados Neo4j a partir do arquivo modelo_dados.py.
    
    Esta função lê o schema definido no arquivo JSON modelo_dados.py que contém:
    - Todos os nós (labels) e suas propriedades
    - Relacionamentos entre os nós
    - Diretrizes e conceitos do modelo de dados
    
    Use esta função sempre que precisar entender a estrutura do grafo
    antes de criar queries Cypher.
    """
    try:
        
        # Ler o arquivo modelo_dados.py existente
        import json
        import os
        
        modelo_path = os.path.join(os.path.dirname(__file__), '..', 'modelo_dados.py')
        with open(modelo_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Extrair o JSON do arquivo Python
            start = content.find('{')
            end = content.rfind('}') + 1
            schema_json = content[start:end]
            schema_data = json.loads(schema_json)
        
        
        # Extrair informações relevantes do schema
        nodes = schema_data.get("nodes", {})
        general_guidelines = schema_data.get("general_guidelines", {})
        
        # Criar lista de labels disponíveis
        labels = []
        for node_name, node_info in nodes.items():
            node_labels = node_info.get("labels", [])
            labels.extend(node_labels)
        
        # Remover duplicatas e ordenar
        labels = sorted(list(set(labels)))
        
        
        return {
            "success": True,
            "schema": {
                "nodes": nodes,
                "labels": labels,
                "guidelines": general_guidelines,
                "total_nodes": len(nodes),
                "total_labels": len(labels)
            },
            "message": "Schema do modelo de dados obtido com sucesso do arquivo JSON. Use essas informações para criar queries Cypher adequadas."
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao consultar schema do modelo de dados: {e}")
        return {
            "success": False,
            "message": f"Erro ao consultar schema: {str(e)}"
        }



async def executar_cypher_leitura(query: str, params: Optional[dict] = None):
    """
    Executa uma query Cypher de LEITURA no banco Neo4j.
    
    Use esta função para queries que CONSULTAM dados, como:
    - MATCH para buscar nós e relacionamentos
    - RETURN para retornar resultados
    - WHERE para filtrar resultados
    - ORDER BY, LIMIT, SKIP para organizar resultados
    
    Args:
        query (str): Query Cypher de leitura (deve começar com MATCH)
        params (dict, optional): Parâmetros para a query
    
    IMPORTANTE: Apenas queries de leitura são permitidas (MATCH, RETURN, etc.).
    Queries de escrita (CREATE, MERGE, SET, DELETE) devem usar executar_cypher_escrita().
    """
    try:
        
        # Usar função MCP que já existe e funciona
        from ..functions.mcp_functions import mcp_neo4j_read_neo4j_cypher
        
        # Executar a query usando a função MCP real
        resultado = mcp_neo4j_read_neo4j_cypher(query, params or {})
        
        return {
            "success": True,
            "query": query,
            "params": params,
            "resultado": resultado,
            "message": "Query de leitura executada com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ ERRO GERAL ao executar query de leitura: {e}")
        import traceback
        logger.error(f"❌ Traceback completo: {traceback.format_exc()}")
        return {
            "success": False,
            "query": query,
            "params": params,
            "message": f"Erro ao executar query de leitura: {str(e)}"
        }


@function_tool
async def gera_idx():
    """
    Gera um identificador único (IDX) para ser usado como propriedade dos nós no Neo4j.
    
    Esta função utiliza generate_unique_id() para criar um identificador único
    baseado em timestamp e número aleatório.
    
    Returns:
        str: Identificador único no formato de 10 dígitos
    
    Use esta função sempre que precisar criar um novo nó no Neo4j para
    garantir que ele tenha uma propriedade idx única.
    """
    try:
        
        # Importar e usar a função generate_unique_id existente
        from ..functions.util import generate_unique_id
        
        # Gerar o IDX único
        novo_idx = generate_unique_id()
        
        
        return {
            "success": True,
            "idx": novo_idx,
            "message": f"IDX único gerado: {novo_idx}"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao gerar IDX: {e}")
        return {
            "success": False,
            "message": f"Erro ao gerar IDX: {str(e)}"
        }


#@function_tool
async def executar_cypher_escrita(query: str, params: Optional[dict] = None):
    """
    Executa uma query Cypher de ESCRITA no banco Neo4j.
    
    Use esta função para queries que MODIFICAM dados, como:
    - CREATE para criar novos nós e relacionamentos
    - MERGE para criar ou encontrar nós/relacionamentos
    - SET para atualizar propriedades
    - DELETE para remover nós e relacionamentos
    - REMOVE para remover propriedades ou labels
    
    Args:
        query (str): Query Cypher de escrita (CREATE, MERGE, SET, DELETE, etc.)
        params (dict, optional): Parâmetros para a query
    
    IMPORTANTE: Apenas queries de escrita são permitidas. 
    Para consultas, use executar_cypher_leitura().
    
    ATENÇÃO: Estas operações modificam permanentemente o banco de dados.
    """
    try:
        
        # Usar função MCP que já existe e funciona
        from ..functions.mcp_functions import mcp_neo4j_write_neo4j_cypher
        
        # Executar query de escrita usando a função MCP real
        resultado = mcp_neo4j_write_neo4j_cypher(query, params or {})
        
        
        return {
            "success": True,
            "query": query,
            "params": params,
            "resultado": resultado,
            "message": "Query de escrita executada com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao executar query de escrita: {e}")
        return {
            "success": False,
            "query": query,
            "params": params,
            "message": f"Erro ao executar query de escrita: {str(e)}"
        }


# ===============================================================================
# FUNÇÕES UTILITÁRIAS PARA GERENCIAMENTO DE CONVERSAS
# ===============================================================================

def get_conversa_key(negocio_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{negocio_idx}_{agente_nome}_{conversa_idx}"


def find_active_conversation(negocio_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{negocio_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


# ===============================================================================
# ENDPOINTS DA API
# ===============================================================================

@router.post("/send/text")
async def send_text_neo4j(data: dict):
    """
    Endpoint principal para processar mensagens de texto para o agente Neo4j
    Converte linguagem natural em queries Cypher e executa no banco
    """
    
    try:
        # Extrair dados necessários
        mensagem = data.get("mensagem", "")
        modelo = data.get("modelo") or "1234567890"
        usuario_nome = data.get("usuario_nome", "Neo4j User")
        negocio_idx = data.get("negocio_idx", "")
        usuario_idx = data.get("usuario_idx")
        agente_flag = data.get("agente", None)

        if not mensagem:
            return {"success": False, "message": "Mensagem é obrigatória"}

        if not modelo:
            return {"success": False, "message": "Modelo é obrigatório"}

        if usuario_idx != "1122334455":
            return {"success": False, "message": "Você não tem permissão para usar este agente"}

        # Verificar se existe conversa ativa
        conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "neo4j")

        if not conversa_idx:
            conversa_idx = generate_unique_id()
            historico_mensagens = []

        # Adicionar mensagem do usuário ao histórico
        historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)

        # Criar agente Neo4j
        agentNeo4j = AgentNeo4j(
            negocio_idx=negocio_idx,
            usuario_nome=usuario_nome
        )

        # Carregar modelo
        llm = LLM()
        model = llm.get_model_idx(modelo)

        instructions = agentNeo4j.get_instructions()

        # Configurar agente
        agente_config = {
            "name": "Neo4j Query Agent",
            "instructions": instructions,
            "model": model,
            "tools": [
                consultar_schema_neo4j,
                gera_idx, 
                executar_cypher_leitura,
                executar_cypher_escrita,
                
            ],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
        }

        # Criar o agente
        #logger.info(f"🔧 Criando agente Neo4j com configurações: {agente_config}")
        #logger.info("vou criar o agente")
     
        agente_obj = await oai.agent_create(**agente_config)
        

        # Se for um agente (flag presente), retorna resposta no padrão status/messages/message
        if agente_flag:
            resposta_completa = ""
            try:
                async for chunk in oai.agent_run(agente_obj, historico_mensagens):
                    if isinstance(chunk, bytes):
                        chunk_str = chunk.decode('utf-8')
                    else:
                        chunk_str = str(chunk)
                    resposta_completa += chunk_str
                # Adicionar resposta ao histórico
                historico_mensagens = add_message_to_history(historico_mensagens, resposta_completa, False)
                # Salvar histórico no cache
                cache_key = get_conversa_key(negocio_idx, "neo4j", conversa_idx)
                cache[cache_key] = historico_mensagens
                # Tenta decodificar resposta_completa como JSON
                try:
                    resposta_json = json.loads(resposta_completa)
                    # Se for dict e tiver 'status' e 'messages', retorna direto
                    if isinstance(resposta_json, dict) and 'status' in resposta_json and 'messages' in resposta_json:
                        return resposta_json
                except Exception:
                    pass
                # Se não for JSON válido ou não tiver o formato esperado, encapsula
                return {
                            "status": "success",
                            "message": resposta_completa
                        }
            except Exception as e:
                logger.error(f"Erro durante execução sem streaming: {str(e)}")
                return {
                    "status": "fail",
                    "messages": [
                        {
                            "status": "fail",
                            "message": f"Erro durante execução sem streaming: {str(e)}"
                        }
                    ]
                }

        # Caso contrário, retorna streaming
        async def event_stream():
            resposta_completa = ""
            nonlocal historico_mensagens  # ← CORREÇÃO: Permite acesso à variável do escopo externo
            try:
                async for chunk in oai.agent_run(agente_obj, historico_mensagens):
                    if isinstance(chunk, bytes):
                        chunk_str = chunk.decode('utf-8')
                    else:
                        chunk_str = str(chunk)
                    resposta_completa += chunk_str
                    yield chunk_str
                # Adicionar resposta ao histórico
                historico_mensagens = add_message_to_history(historico_mensagens, resposta_completa, False)
                # Salvar histórico no cache
                cache_key = get_conversa_key(negocio_idx, "neo4j", conversa_idx)
                cache[cache_key] = historico_mensagens
            except Exception as stream_error:
                logger.error(f"Erro durante o streaming: {str(stream_error)}")
                yield f"Erro: {str(stream_error)}"

        return StreamingResponse(event_stream(), media_type="text/plain")

    except Exception as e:
        logger.error(f"Erro no endpoint send_text_neo4j: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "message": f"Erro interno: {str(e)}"}


@router.post("/drop/messages")
async def drop_messages_neo4j(data: dict):
    """
    Endpoint para limpar cache de conversas do agente Neo4j
    
    Parâmetros:
    - negocio_idx (str): ID do negócio (obrigatório)
    """
    
    negocio_idx = data.get("negocio_idx", "")
    
    if not negocio_idx:
        return {"success": False, "message": "negocio_idx é obrigatório"}
    
    try:
        chaves_removidas = 0
        chaves_para_remover = []
        
        # Buscar chaves do cache que correspondem ao agente Neo4j
        for key in list(cache.keys()):
            # Filtrar por conversas do agente neo4j
            if key.startswith(f"conversa_{negocio_idx}_neo4j_"):
                chaves_para_remover.append(key)
        
        # Remover as chaves identificadas do cache
        for key in chaves_para_remover:
            try:
                del cache[key]
                chaves_removidas += 1
            except KeyError:
                logger.warning(f"Chave já não existe no cache: {key}")
        
        
        return {
            "success": True,
            "message": f"Cache de conversas Neo4j limpo com sucesso! {chaves_removidas} conversas removidas.",
            "detalhes": {
                "conversas_removidas": chaves_removidas,
                "filtro_usado": f"negocio_idx: {negocio_idx}"
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao limpar cache: {str(e)}")
        return {
            "success": False,
            "message": f"Erro ao limpar cache: {str(e)}"
        } 



if __name__ == "__main__":
    import asyncio

    async def  teste():
        data = {
            "mensagem": "oi",
            "negocio_idx": "7600786155",
            "modelo": "1234567895",
            "usuario_idx": "4344140157",
            "agente": "@gptalkzap",
            "usuario_idx": "1122334455"
       
        }

        result = await send_text_neo4j(data)
        #print("result", result)
    async def teste2():
        pass

    async def teste_pipeline_embedding():
        # --- Texto de Exemplo para Ingestão ---
        # Usaremos um texto simples para focar no funcionamento da biblioteca.
        # A lógica para extrair texto de um vídeo do YouTube (Passo 1) permanece a mesma.
        
        print("teste_pipeline_embedding()")

        TEXTO_PARA_PROCESSAR = """
        A Acme Corporation, fundada em 1985 por John Smith em Nova York, é líder em widgets.
        Sarah Johnson, que entrou em 2010, agora é a Diretora de Engenharia.
        O produto principal, SuperWidget X1, foi desenvolvido pela equipe de Sarah
        """
        

        # --- Definição do Nosso Esquema (Nossa "Planta da Casa") ---
        # Este é o esquema que desenhamos juntos, agora formatado para a biblioteca.
        # A biblioteca usará isso para guiar o LLM na extração.
        NOSSO_ESQUEMA = {
            "node_types": [
                {"label": "Person", "properties": [{"name": "name", "type": "STRING"}]},
                {"label": "Organization", "properties": [{"name": "name", "type": "STRING"}]},
                {"label": "Product", "properties": [{"name": "name", "type": "STRING"}]},
                {"label": "Location", "properties": [{"name": "name", "type": "STRING"}]}
            ],
            "relationship_types": [
                "WORKS_FOR",
                "FOUNDED_BY",
                "LOCATED_IN",
                "DEVELOPED"
            ],
            "patterns": [
                ("Person", "WORKS_FOR", "Organization"),
                ("Organization", "FOUNDED_BY", "Person"),
                ("Organization", "LOCATED_IN", "Location"),
                ("Product", "DEVELOPED", "Person")
            ]
        }
        neo4j = AgentNeo4j()
        driver = await neo4j.conecta()

        kg_builder = SimpleKGPipeline(
            llm=oai.get_llm_for_neo4j(
                base_url="https://api.openai.com/v1",
                api_key=oai.secret.OPENAI_API_KEY,
                model_name="gpt-4o"
            ),
            driver=driver,
            embedder=OpenAIEmbeddings(api_key=oai.secret.OPENAI_API_KEY),
            schema=NOSSO_ESQUEMA,
            from_pdf=False,  # Estamos usando texto, não um arquivo PDF
            on_error="RAISE", # Se o LLM falhar, o processo para.
            neo4j_database="neo4j"
        )

        print("Pipeline criado, executando a ingestão...")
        
        try:
            # Executar o pipeline com o texto
            result = await kg_builder.run_async(text=TEXTO_PARA_PROCESSAR)
            
            #print("\n--- ✅ Pipeline Concluído com Sucesso ---")
            #print(f"ID da Execução: {result.run_id}")
            #print(f"Resultado Final: {result.result}")
            
            # Verificar os resultados no banco
            with driver.session(database="neo4j") as session:
            # Consultar nós de entidade criados
                nodes_result = session.run("MATCH (n:__Entity__) RETURN n.name AS name, labels(n) AS labels")
                #print("\nNós de Entidade Criados:")
                node_count = 0
                for record in nodes_result:
                    # Filtrar labels do neo4j-graphrag para mostrar apenas os tipos de entidade
                    entity_labels = [label for label in record['labels'] if label not in ['__KGBuilder__', '__Entity__']]
                    entity_type = entity_labels[0] if entity_labels else 'Unknown'
                    print(f"- Nó: {entity_type}, Nome: {record['name']}")
                    node_count += 1
                
                if node_count == 0:
                    print("  Nenhum nó de entidade encontrado.")
                
                # Consultar relações criadas pelo pipeline
                rels_result = session.run(
                    "MATCH (n:__Entity__)-[r]->(m:__Entity__) "
                    "RETURN n.name AS start, type(r) AS type, m.name AS end"
                )
                print("\nRelações Criadas pelo Pipeline:")
                rel_count = 0
                for record in rels_result:
                    print(f"- Relação: ({record['start']})-[:{record['type']}]->({record['end']})")
                    rel_count += 1
                
                if rel_count == 0:
                    print("  Nenhuma relação entre entidades encontrada.")
                    
                # Estatísticas gerais
                stats_result = session.run(
                    "MATCH (n) RETURN labels(n) AS labels, count(n) AS count"
                )
                #print("\nEstatísticas do Grafo:")
                #for record in stats_result:
                    #labels = ", ".join(record['labels'])
                    #print(f"- {labels}: {record['count']} nós")
                    
            return result
            
        except Exception as e:
            print(f"\n❌ ERRO durante a execução do pipeline: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return None
            
        finally:
            # Fechar conexão
            await neo4j.close()



        

    #asyncio.run(teste())
    asyncio.run(teste2())
    #asyncio.run(teste_pipeline_embedding())

        
        