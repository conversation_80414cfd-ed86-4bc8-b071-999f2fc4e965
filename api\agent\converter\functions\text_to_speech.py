import logging
import asyncio
import sys
import os
import base64
from typing import Optional
from ...agent_neo4j import AgentNeo4j

neo4j = AgentNeo4j()


# Adiciona o diretório raiz do projeto ao path do Python
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# Configuração básica de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Importação da classe AgentTTSEdge
try:
    from api.agent.agent_ttsEdge import AgentTTSEdge
    tts_agent = AgentTTSEdge()
except ImportError as e:
    logger.error(f"Erro ao importar AgentTTSEdge: {str(e)}")
    raise

async def limpar_emojis_para_tts(text: str) -> str:
    """Remove emojis do texto para o TTS"""
    # Implementação básica de limpeza de emojis
    import re
    emoji_pattern = re.compile("[" 
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # símbolos e pictogramas
        u"\U0001F680-\U0001F6FF"  # transporte e símbolos
        u"\U0001F1E0-\U0001F1FF"  # bandeiras (iOS)
                           "]+", flags=re.UNICODE)
    return emoji_pattern.sub(r'', text)

async def text_to_speech(text: str, voz_idx: str = "") -> str:
    """
    Converte texto para áudio usando AgentTTSEdge
    
    Args:
        text: Texto a ser convertido em fala
        voice: Voz a ser usada (padrão: "pt-BR-FranciscaNeural")
        
    Returns:
        str: Áudio codificado em base64 ou string vazia em caso de erro
    """

    
    voz = await carrega_voz(voz_idx)  

    
    try:
        import base64
        
        # Limpa o texto de emojis
        texto_limpo = await limpar_emojis_para_tts(text)
        
        # Usa a instância global do agente TTS
        result = await tts_agent.synthesize_to_bytes(texto_limpo, voz.get("nome"))
        
        if result and result.get("success") and result.get("audio_data"):
            audio_bytes = result["audio_data"]
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
            logger.info(f" TTS: Texto convertido para áudio usando voz { voz.get("nome")} - {len(audio_bytes)} bytes")
            return audio_base64
        
        logger.warning(" TTS: Nenhum áudio gerado pelo AgentTTSEdge")
        return ""
        
    except Exception as e:
        logger.error(f" TTS: Erro na síntese de voz: {str(e)}", exc_info=True)
        return ""


async def carrega_voz(voz_idx: str = "") -> dict:
    """
    Carrega os dados de uma voz do banco de dados Neo4j usando o voz_idx.
    Retorna apenas os dados da voz em JSON em caso de sucesso.
    """
    if not voz_idx:
        return {}

    query = """
        MATCH (v:Voz {idx: $idx})
        RETURN v
        """
    parameters = {"idx": voz_idx}

    try:
        # Importar o módulo neo4j do projeto
    
        # Executar a query
        result = mcp_neo4j_read_neo4j_cypher(query, parameters)

        # Verificar se encontrou a voz
        if result and len(result) > 0:
            # Extrair os dados da voz
            voz_node = result[0].get('v')
            if voz_node:
                # Converter para dicionário Python
                voz_data = dict(voz_node)
                return voz_data
            else:
                logger.warning(f"Nó da voz não encontrado na resposta para idx: {voz_idx}")
                return {}
        else:
            logger.warning(f"Nenhuma voz encontrada com idx: {voz_idx}")
            return {}

    except Exception as e:
        logger.error(f"Erro ao carregar dados da voz {voz_idx}: {e}")
        return {}





async def test_text_to_speech():
    """Função de teste para text_to_speech"""
    # Texto de exemplo para teste
    texto_teste = "Olá, este é um teste de conversão de texto para fala com Python!"
    
    print(f" Iniciando teste de conversão de texto para fala...")
    print(f" Texto: {texto_teste}")
    
    # Chamando a função de conversão
    audio_base64 = await text_to_speech(texto_teste)
    
    if audio_base64:
        print(" Conversão concluída com sucesso!")
        print(f" Tamanho do áudio em base64: {len(audio_base64)} caracteres")
        
        # Opcional: Salvar o áudio em um arquivo para teste
        try:
            import os
            from datetime import datetime
            
            # Criar diretório de saída se não existir
            output_dir = "output_audio"
            os.makedirs(output_dir, exist_ok=True)
            
            # Nome do arquivo com timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(output_dir, f"tts_output_{timestamp}.wav")
            
            # Escrever o áudio em um arquivo
            with open(output_file, "wb") as f:
                f.write(base64.b64decode(audio_base64.encode('utf-8')))
                
            print(f" Áudio salvo em: {os.path.abspath(output_file)}")
            
        except Exception as e:
            print(f" Erro ao salvar o áudio: {str(e)}")
    else:
        print(" Falha na conversão de texto para fala")

if __name__ == "__main__":
    try:
        # Executar o teste de forma assíncrona
        asyncio.run(test_text_to_speech())
    except Exception as e:
        logger.error(f"Erro ao executar os testes: {str(e)}", exc_info=True)
        raise