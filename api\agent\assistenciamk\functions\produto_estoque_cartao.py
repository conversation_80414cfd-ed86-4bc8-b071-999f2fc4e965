import json
import os
import asyncio
import re

    
from api.agent.agent import salva_mensagem
from ...agent_neo4j import AgentNeo4j
from ...agent_evolutionzap import AgentEvolutionZap
from agents.tool import function_tool
import platform
from ...agent_secret import Secret
from .verificar_estoque_revenda import verificar_estoque_revenda as produto_estoque_consulta

# ---------- CACHE GLOBAL ----------
cache_var = {}   # int -> variação original
var_cnt   = 1    # contador global

secret = Secret()
is_local = platform.system() == "Windows"
intancia_id = ""
if is_local:
    instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID_LOCAL")
else:
    instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID")

evolutionApi = AgentEvolutionZap()

# Carregar o esquema do Neo4j
#schema_path = os.path.join(os.path.dirname(__file__), 'schemas', 'produto_estoque_cartao_neo4j.json')
#with open(schema_path, 'r', encoding='utf-8') as f:
#    produto_estoque_cartao_neo4j = json.load(f)

# Inicializar cliente Neo4j
neo4j = AgentNeo4j()

@function_tool
async def produto_estoque_cartao(
                             usuario_whatsapp: str,
                             tipo_resposta: str, 
                             mensagem_inicial: str,
                             mensagem_final: str,
                             em_resposta_idx: str,
                             agente_idx: str,
                             conversa_idx: str,
                             usuario_idx: str,
                             canal_idx: str,
                             negocio_idx: str,
                             termos: str,
                             ):
    """
    Gera lista de produtos em formato de cartão (cartão de produto) para WhatsApp, com base em consulta ao estoque de revenda da consultora. 
   
    A função executa a consulta no Neo4j usando uma QUERY PADRÃO FIXA e ela mesma envia para o usuário, não sendo necessário ao agente informar nada mais ao usuário.

    ===============================================================================
    🚨🚨🚨 MUDANÇA IMPORTANTE - LEIA COM ATENÇÃO 🚨🚨🚨
    ===============================================================================
    O agente apenas fornece os PARÂMETROS necessários para a query predefinida.
    ===============================================================================

    ===============================================================================
    🚨🚨🚨 REGRA CRÍTICA - LEIA COM ATENÇÃO 🚨🚨🚨
    ===============================================================================
    TODAS as consultas DEVEM retornar as variações de cor, MESMO QUE HAJA APENAS UM PRODUTO.
    Isso é OBRIGATÓRIO para o correto funcionamento do sistema.
    NUNCA use uma consulta que não retorne as variações, mesmo para um único produto.
    ===============================================================================

    PARÂMETROS
    ----------
    1. canal - str. obrigatório   
    identificador do canal (whatsapp, web_app, etc.)
    
    2. tipo_resposta - str. obrigatório   
    opções: 'cartao' | 'livre' -  
    Informa se haverá dados cadastrais de um ou mais produtos (cartao) ou um texto apenas com alguma informação  
    
    3. mensagem_inicial: str - opcional 
    mensagem que antecede aos dados. Visa apresentar ou dizer o que são os dados.
    Exemplo: "Encontrei estes 4 produtos que são da cor que solicitou:"
    
    4. mensagem_final: str - opcional
    mensagem que procede os dados. Normalmente um comentário ou explicação dos dados.
    Exemplo: "Para compartilhar com o cliente, siga este procedimento..."
    
    5. parametros: str - obrigatório 
    String JSON com os parâmetros . Exemplo:

    6. usuario_idx: str - obrigatório
    idx do usuário que está solicitando a consulta

    7. negocio_idx: str - obrigatório
    idx do negócio para o qual a consulta será feita
    
    

    
    
    ===============================================================================
    PROCESSO PARA O AGENTE:
    ===============================================================================
    
    1. **Identificar os termos** da solicitação do usuário
       Exemplo: "liste os batons da marca mate" → termos: "batons" e "mate"
    
    2. **Corrigir termos** que estão claramente escritos de forma errada
       Exemplo: "batttons" → "batons"
    
    3. **Passar os termos para o singular**
       Exemplo: "batons" → "batom"
    
    4. **Criar o termo de pesquisa**
       
       🚨🚨🚨 REGRA IMPORTANTE PARA CÓDIGOS E TERMOS 🚨🚨🚨
       
       **PARA CÓDIGOS DE PRODUTOS:**
       - Códigos devem ser NÚMEROS EXATOS, SEM busca fonética
       - NÃO adicione o símbolo ~ (til) após códigos
       - Use o código exatamente como fornecido
       
       ❌ ERRO: {"10102674~"}
       ✅ CORRETO: { "10102674"}
       
       **PARA NOMES/TERMOS DE PRODUTOS:**
       - Use busca PARCIAL sem busca fonética
       - NÃO use o símbolo ~ (til) - isso ativa busca fonética
       - Use apenas o termo sem modificadores para busca parcial
       - Exemplo: "batom" (não "batom~")
       
       **COMO IDENTIFICAR:**
       - Se for apenas números = é código (sem modificadores)
       - Se contém letras = é nome/termo (sem modificadores para busca parcial)
       
       **REGRAS PARA TERMOS MÚLTIPLOS:**
       - Use AND para buscar produtos que contenham TODOS os termos
       - Use OR para buscar produtos que contenham PELO MENOS um dos termos
       - Separe os termos com espaços após AND/OR
       - Máximo de 3-4 termos por busca para evitar resultados muito restritos
       - Exemplos:
         * "batom AND matte AND vermelho" (busca produtos com todos os termos)
         * "batom OR gloss OR lip" (busca produtos com pelo menos um termo)
         * "10102674 OR batom" (busca mista: código OU termo)
   
       
       Para busca por nome/termo (busca parcial):
       "batom AND matte"
          
       Para busca mista (código E termo):
       "10102674 OR batom"
       
       **Exemplos com termos múltiplos:**
       "batom AND matte AND vermelho"
        "gloss OR batom OR lip"
    
    ===============================================================================
    ESTRUTURA DO BANCO DE DADOS (NEO4J):
    ===============================================================================
    - Um NEGÓCIO (Negocio) possui vários PRODUTOS (relacionamento POSSUI_PRODUTO)
    - Um PRODUTO pode ter várias CORES (relacionamento TEM_COR)
    - Um NEGÓCIO disponibiliza CORES específicas (relacionamento DISPONIBILIZA_COR)

    ===============================================================================
    EXEMPLO DE RESULTADO CORRETO:
    ===============================================================================
    {
      'nome': 'Batom X',
      'preco': 50.0,
      'estoque': 20,
      'variacoes': [
        {'codigo': '123', 'cor': 'Vermelho'},
        {'codigo': '124', 'cor': 'Azul'}
      ]
    }



    Retorno
    -------
    json com as chaves:
        status: success ou error
        message: string com dados do resultado ou mensagem de erro
       
    🚨🚨🚨 A RESPOSTA DEVE SER ENVIADA EXATAMENTE ASSIM, SEM ALTERAÇÕES. O USUÁRIO ESPERA RECEBER UM JSON COM RESPOSTA. ENTREGUE, PORTANTO, A STRING DO JSON RECEBIDO COMO RESPOSTA, SEM AJUSTES, SEM CONVERSÕES, SEM COMENTÁRIOS.
    """
    
    print("==============================")  
    print("#### @@@@@ ####### @@@@@@@@ @@@@@@ produto_estoque_cartao()")
    print("===============================")
    print("usuario_whatsapp", usuario_whatsapp)
    print("usuario_idx", usuario_idx)
    print("negocio_idx",negocio_idx)
    print("instancia_id", instancia_id)
    print("tipo_resposta", tipo_resposta)
    print("mensagem_inicial", mensagem_inicial) 
    print("mensagem_final", mensagem_final)
    print("em_resposta_idx", em_resposta_idx)
    print("canal_idx", canal_idx)
    print("@@@@@@@@   parametros", termos)    

    
    
    # Executar a query padrão fixa
    try:
        parametros = {
            "usuario_idx": usuario_idx,
            "negocio_idx": negocio_idx,
            "termo_consulta": termos
        }


        parametros = json.dumps(parametros)
        resultado = await verificar_estoque_revenda(parametros,termos)
        print("@@@@@ resultado da produto_estoque_cartao:", resultado)
    except Exception as e:
        return {
            "status": "error", 
            "message": f"Erro ao executar consulta: {str(e)}"
        }
    
    if not resultado or not resultado.get("data"):
        mensagem = "produto não encontrado"
        await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem, instancia_id)
        return {"status": "success", 
        "message": "Nenhum produto encontrado.",
        "function": "produto_estoque_cartao"
        }  
    
    
    # Enviar mensagens via WhatsApp
    await enviar_mensagens_zap(
        whatsapp=usuario_whatsapp,
        mensagem_inicial=mensagem_inicial,
        mensagem_final=mensagem_final,
        dados=resultado["data"],
        instancia_id=instancia_id,
        conversa_idx=conversa_idx,
        usuario_idx=usuario_idx,
        agente_idx=agente_idx,
        em_resposta_idx=em_resposta_idx,
        canal_idx=canal_idx
    )

    # Monta a resposta final com todos os campos necessários
    resposta = {
        "status": "success",
        "message": resultado,  # ✅ CORREÇÃO: string vazia pois já enviou as mensagens
        "function": "produto_estoque_cartao"
    }   

    
    print("@@@@@ resposta:", resposta)
    return resposta

def sanitizar_mensagem_para_banco(mensagem: str) -> str:
    """
    Sanitiza mensagem formatada para evitar problemas no banco de dados Neo4j.
    
    Args:
        mensagem (str): Mensagem original com formatação Markdown
        
    Returns:
        str: Mensagem sanitizada para armazenamento seguro
    """
    if not mensagem:
        return ""
    
    # Escapar aspas simples e duplas que podem quebrar queries Cypher
    mensagem_sanitizada = mensagem.replace("'", "\\'").replace('"', '\\"')
    
    # Escapar barras invertidas
    mensagem_sanitizada = mensagem_sanitizada.replace("\\", "\\\\")
    
    # Normalizar quebras de linha para evitar problemas de parsing
    mensagem_sanitizada = mensagem_sanitizada.replace("\r\n", "\n").replace("\r", "\n")
    
    # Remover caracteres de controle problemáticos (mantendo quebras de linha)
    mensagem_sanitizada = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', mensagem_sanitizada)
    
    # Limitar tamanho da mensagem para evitar problemas de memória
    if len(mensagem_sanitizada) > 10000:
        mensagem_sanitizada = mensagem_sanitizada[:10000] + "... [mensagem truncada]"
    
    return mensagem_sanitizada 


    
async def enviar_mensagens_zap(whatsapp,mensagem_inicial, mensagem_final, dados,instancia_id,conversa_idx=None,usuario_idx=None,agente_idx=None,em_resposta_idx=None, canal_idx=None):
            #print (f"===== enviar_mensagens()=====")
            #print("mensagem_inicial:" , mensagem_inicial)
            #print("mensagem_final:" , mensagem_final)
            #print("dados" , dados)   

            if mensagem_inicial:
                result = await evolutionApi.send_evolution_message(whatsapp,mensagem_inicial,instancia_id)

                #print("📩 mensagem inicial enviada", result)
            

            # ---------- DENTRO DA FUNÇÃO ----------
                # Após o for que envia mensagens, **salva histórico leve**
            for item in dados:
                    #print("item",item)
                    #print("url_imagem",item['url_imagem'])
                    # 1. Formatação rica para WhatsApp (seu código original)
                    mensagem_whatsapp = formata_whatsapp(item)   # Markdown + emojis
                    #print("")
                    #print("mensagem_whatsapp", mensagem_whatsapp)
                    #print("type(mensagem_whatsapp)",type(mensagem_whatsapp))
                    #print("")
                    # 2. Versão LIGHT para histórico (vai pro LLM)
                    mensagem_historico = compactar_produto_historico(item, max_var_visiveis=3)
                    #print("")
                    #print("mensagem_historico", mensagem_historico)
                    #print("")

                    # 4. Salva **histórico leve** (é esse que o LLM lê depois)
                    await salva_mensagem(
                        mensagem=mensagem_whatsapp,
                        conversa_idx=conversa_idx,
                        usuario_idx=usuario_idx,
                        agente_idx=agente_idx,
                        em_resposta_idx=em_resposta_idx,
                        remetente="agente",
                        destinatario="usuario",
                        canal_idx=canal_idx
                    )


                
                    
                    # Adicionar URL da mídia se disponível
                    
                    if 'url_imagem' in item and item['url_imagem']:
                        #print("tem media_url" , item['url_imagem'])
                    
                        result = await evolutionApi.send_evolution_media(
                                whatsapp,
                                item["url_imagem"],
                                "image",
                                mensagem_whatsapp,
                                instancia_id
                                )
                        #print(f"Resultado: {result}")
                    else:
                        #print("nao tem imagem")
                        result = await evolutionApi.send_evolution_message(
                                whatsapp,
                                mensagem_whatsapp,
                                instancia_id
                        )
                                    



            await asyncio.sleep(2)

                #if mensagem_final:
                    #message = {}
                    #message["status"] = "success"
                    #message["message"] = mensagem_final
                    #result = await evolutionApi.send_evolution_message(whatsapp,mensagem_final,#instancia_id)
                    #print("📩 mensagem final enviada", result)


            #print(" 📩📩📩📩📩 menagens enviadas")        

            return 

def formata_whatsapp(item: dict) -> str:
    """Formatação rica com Markdown e emojis - só para WhatsApp.

    ===============================================================================
    EXEMPLO DE RESULTADO CORRETO:
    ===============================================================================
    {
      'nome': 'Batom X',
      'preco': 50.0,
      'estoque': 20,
      'variacoes': [
        {'codigo': '123', 'cor': 'Vermelho'},
        {'codigo': '124', 'cor': 'Azul'}
      ]
    }


    """

    #print("===== formatata_whatsapp() =====")
    #print("type(item)",type(item))
    #print("item",item)





    msg = f"*{item.get('nome', 'Produto')}*\n"
    if item.get('preco') == item.get('preco_maior') or item.get('preco_maior', 0) == 0:
        msg += f"💵 *Preço:* R$ {item['preco']:.2f}\n"
    elif item.get('preco') < item.get("preco_maior"):
        msg += f"🚨🥳💵  *Preço:*  De ~*R$ {item['preco_maior']:.2f}*~  por apenas  *R$ {item['preco']:.2f}*\n"
    if item.get('variacoes'):
        msg += "\n*Cores disponíveis:*\n"
        for v in item['variacoes']:
            cor   = v.get('cor', 'Cor única')
            cod   = v.get('codigo', '')
            msg  += f"• {cor}"
            if cod:
                msg += f" (Código: {cod})"
            msg += "\n"
    return msg.strip()

# ---------- COMPACTADOR DE PRODUTO MODIFICADO ----------
def compactar_produto_historico(prod: dict, max_var_visiveis: int = 3) -> str:
    """
    Devolve string com TODAS as variações de cor e códigos no formato /codigo.
    Remove limite de cores visíveis.
    """
    global var_cnt

    # 1. nome abreviado
    nome = prod.get("nome", "Produto")
    if len(nome) > 40:
        nome = nome[:37] + "..."

    # 2. preço sem centavos
    preco = prod.get("preco")
    preco_str = f"R${int(preco)}" if preco else ""

    # 3. incluir TODAS as variações com códigos
    var_visiveis = []
    for v in prod.get("variacoes", []):
        idx = var_cnt
        var_cnt += 1
        cache_var[idx] = v
        cor = v.get("cor", "Cor única")
        cod = v.get("codigo", "")
        
        # Formato: [idx] Cor (codigo)
        if cod:
            var_visiveis.append(f"[{idx}] {cor}({cod})")
        else:
            var_visiveis.append(f"[{idx}] {cor}")

    # 4. monta linha única com todas as cores e códigos
    cores_texto = ', '.join(var_visiveis)
    return f"{nome} {preco_str} Cor/codigo: {cores_texto}"

    


# Executar testes quando o arquivo for executado diretamente
if __name__ == "__main__":
    import asyncio

    
    # Caso contrário, executar o teste original da função produto_estoque_cartao
    async def testa_consultar_produtos():
    
        canal_idx = "3245124231"
        whatsapp = "553184198720"
        tipo_resposta = "cartao"
        mensagem_inicial = "Aqui estão os produtos que você solicitou:"
        mensagem_final = "Caso precise de mais informações, só chamar."
        em_resposta_idx = "0129001290"
        agente_idx = "1508250458"
        conversa_idx = "8355885236"
        usuario_idx = "1122334455"
        negocio_idx = "5544332211"
        termos = "10127612"
        

        resultado = await produto_estoque_cartao(whatsapp, tipo_resposta, mensagem_inicial, mensagem_final, em_resposta_idx, agente_idx, conversa_idx, usuario_idx, canal_idx, negocio_idx,termos)

        print("resultado final @@@@@@@", resultado)    
    
    asyncio.run(testa_consultar_produtos())
    #execucao
    
    
    # Para executar apenas os testes da função auxiliar:
    # py -m api.agent.business.functions.produto_estoque_cartao testar_termos
    # py -m api.agent.business.functions.produto_estoque_cartao testar_termos
